import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;

/// A utility script to encrypt the configuration file for secure builds
///
/// This script reads the debug configuration file, encrypts it using AES encryption,
/// and writes the result to the release configuration file.
void main() async {
  try {
    // Define file paths
    final String debugConfigPath = 'assets/config/config_debug.json';
    final String releaseConfigPath = 'assets/config/config.json';

    // Read the debug configuration file
    final File debugConfigFile = File(debugConfigPath);
    if (!await debugConfigFile.exists()) {
      print('Error: Debug configuration file not found at $debugConfigPath');
      exit(1);
    }

    final String debugConfig = await debugConfigFile.readAsString();
    print('Read debug configuration file: $debugConfigPath');

    // Encrypt the configuration
    final EncryptionResult encryptionResult = encryptConfig(debugConfig);
    print('Configuration encrypted successfully using AES-256');

    // Create a JSON object with encryption metadata and the encrypted data
    final Map<String, dynamic> encryptedData = {
      'algorithm': 'AES-256/CBC',
      'iv': base64Encode(encryptionResult.iv),
      'salt': base64Encode(encryptionResult.salt),
      'iterations': encryptionResult.iterations,
      'data': encryptionResult.encryptedData,
      // Add a checksum for integrity verification
      'checksum': sha256.convert(utf8.encode(debugConfig)).toString(),
    };

    // Convert to JSON and then to Base64 for additional obfuscation
    final String encryptedJson = jsonEncode(encryptedData);
    final String finalEncrypted = base64Encode(utf8.encode(encryptedJson));

    // Write the encrypted configuration to the release file
    final File releaseConfigFile = File(releaseConfigPath);
    await releaseConfigFile.writeAsString(finalEncrypted);
    print('Encrypted configuration written to: $releaseConfigPath');

    print('Configuration encryption completed successfully');
  } catch (e) {
    print('Error: $e');
    exit(1);
  }
}

/// Result of the encryption process
class EncryptionResult {
  final String encryptedData;
  final Uint8List iv;
  final Uint8List salt;
  final int iterations;

  EncryptionResult({
    required this.encryptedData,
    required this.iv,
    required this.salt,
    required this.iterations,
  });
}

/// Encrypt the configuration string using AES-256 encryption
///
/// Uses AES encryption with a randomly generated IV and salt
EncryptionResult encryptConfig(String config) {
  // Generate a random salt for key derivation
  final Random random = Random.secure();
  final Uint8List salt = Uint8List(16);
  for (int i = 0; i < 16; i++) {
    salt[i] = random.nextInt(256);
  }

  // Use PBKDF2 for key derivation with a hardcoded passphrase
  // In a real-world scenario, this passphrase would be stored more securely
  final String passphrase = 'Top3SecureConfigEncryption2023!';
  final int iterations = 10000;
  final Uint8List keyBytes = _deriveKey(passphrase, salt, iterations);

  // Create an encryption key from the derived bytes
  final encrypt.Key key = encrypt.Key(keyBytes);

  // Generate a random IV (Initialization Vector)
  final encrypt.IV iv = encrypt.IV.fromSecureRandom(16);

  // Create an encrypter with AES in CBC mode
  final encrypt.Encrypter encrypter = encrypt.Encrypter(
    encrypt.AES(key, mode: encrypt.AESMode.cbc),
  );

  // Encrypt the data
  final encrypt.Encrypted encrypted = encrypter.encrypt(config, iv: iv);

  return EncryptionResult(
    encryptedData: encrypted.base64,
    iv: iv.bytes,
    salt: salt,
    iterations: iterations,
  );
}

/// Derive an encryption key using PBKDF2
Uint8List _deriveKey(String passphrase, Uint8List salt, int iterations) {
  // Use HMAC-SHA256 for key derivation
  final Uint8List passphraseBytes = utf8.encode(passphrase) as Uint8List;

  // Implement a simple PBKDF2 key derivation
  Uint8List result = Uint8List(32); // 256 bits
  Uint8List block = Uint8List(0);

  for (int i = 1; i <= 1; i++) {
    // Initial hash
    Uint8List u = Uint8List.fromList([
      ...salt,
      ...Uint8List.fromList([0, 0, 0, i])
    ]);

    for (int j = 0; j < iterations; j++) {
      u = Uint8List.fromList(
        Hmac(sha256, passphraseBytes).convert(u).bytes
      );

      if (j == 0) {
        block = Uint8List.fromList(u);
      } else {
        for (int k = 0; k < u.length; k++) {
          block[k] ^= u[k];
        }
      }
    }

    // Copy the block to the result
    for (int j = 0; j < block.length && j < result.length; j++) {
      result[j] = block[j];
    }
  }

  return result;
}
