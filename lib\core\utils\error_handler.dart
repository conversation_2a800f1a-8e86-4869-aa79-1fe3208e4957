import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../error/exceptions.dart';
import 'logger.dart';

class ErrorHandler {
  static void showErrorDialog(
    BuildContext context, {
    required String title,
    required String message,
    String buttonText = 'OK',
    VoidCallback? onPressed,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          content: Text(
            message,
            style: const TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (onPressed != null) {
                  onPressed();
                }
              },
              child: Text(
                buttonText,
                style: const TextStyle(color: Colors.black),
              ),
            )
          ],
        );
      },
    );
  }

  static void showToast(
    String message, {
    Toast length = Toast.LENGTH_SHORT,
    ToastGravity gravity = ToastGravity.BOTTOM,
  }) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: length,
      gravity: gravity,
      timeInSecForIosWeb: 1,
      backgroundColor: Colors.black87,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  static void handleException(
    BuildContext context,
    dynamic exception, {
    String? fallbackMessage,
    bool showDialog = true,
    bool logError = true,
  }) {
    String errorMessage = _getErrorMessage(exception, fallbackMessage);

    if (logError) {
      Logger.error('Error occurred', error: exception);
    }

    if (showDialog) {
      showErrorDialog(
        context,
        title: 'Error',
        message: errorMessage,
      );
    } else {
      showToast(errorMessage);
    }
  }

  static String _getErrorMessage(dynamic exception, String? fallbackMessage) {
    if (exception is AuthException) {
      return 'Authentication error: ${exception.message}';
    } else if (exception is NetworkException) {
      return 'Network error: Please check your internet connection';
    } else if (exception is ServerException) {
      return 'Server error: Please try again later';
    } else if (exception is ApiException) {
      return 'API error: ${exception.message}';
    } else if (exception is CacheException) {
      return 'Cache error: ${exception.message}';
    } else if (exception is Exception) {
      return exception.toString().replaceAll('Exception: ', '');
    } else {
      return fallbackMessage ?? 'An unexpected error occurred';
    }
  }
}
