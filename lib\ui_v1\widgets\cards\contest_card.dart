import 'package:flutter/material.dart';
import '../../../backend/model/contest.dart';
import '../../../backend/model/match.dart';
import '../../utils/constants.dart';
import '../utils/contest_app_bar.dart';

class ContestCard extends StatelessWidget {
  final Match? match;
  final indianRupeeSymbol = '\u20B9';
  final ContestAppBar contestAppBar;
  final Contest contest;
  const ContestCard(this.contest, this.match, this.contestAppBar, {super.key});

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    final int total =
        int.parse(contest.entry) * int.parse(contest.numberOfUsersJoined);
    final int pool = (total * 0.8).round();
    final int firstPrize = (total * 0.5).round();
    final int secondPrize = (total * 0.3).round();
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(screenWidth * 0.02),
      ),
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(screenWidth * 0.02),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.02),
              child: Text(
                'Entry Fee: ${contest.entry}',
                style: TextStyle(
                    fontSize: screenWidth * 0.045,
                    fontWeight: FontWeight.bold), // 4% of screen width
              ),
            ),
            SizedBox(height: screenHeight * 0.01), // 1% of screen height
            Padding(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
              child: Row(
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.join_full_outlined,
                        color: Color.fromARGB(97, 0, 9, 12),
                      ),
                      SizedBox(width: screenWidth * 0.01), // 1% of screen width
                      Text(
                        'Pool: $indianRupeeSymbol $pool',
                        style: TextStyle(
                            fontSize:
                                screenWidth * 0.035), // 3% of screen width
                      ),
                    ],
                  ),
                  SizedBox(width: screenWidth * 0.035), // 2% of screen width
                  Row(
                    children: [
                      const Icon(
                        Icons.emoji_events,
                        color: Color.fromARGB(255, 253, 213, 12),
                      ),
                      SizedBox(width: screenWidth * 0.01),
                      Text(
                        '$indianRupeeSymbol $firstPrize',
                        style: TextStyle(fontSize: screenWidth * 0.035),
                      ),
                    ],
                  ),
                  SizedBox(width: screenWidth * 0.035),
                  Row(
                    children: [
                      const Icon(
                        Icons.emoji_events_outlined,
                        color: Color.fromARGB(255, 174, 152, 74),
                      ),
                      SizedBox(width: screenWidth * 0.01),
                      Text(
                        '$indianRupeeSymbol $secondPrize',
                        style: TextStyle(fontSize: screenWidth * 0.035),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
              child: const Divider(
                thickness: 1,
                color: Colors.grey,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  buildAvatarRow(screenWidth, contest.numberOfUsersJoined),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pushNamed(context, RoutePaths.showContest,
                          arguments: {contest, match, contestAppBar});
                    },
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all<Color>(
                        const Color.fromARGB(255, 48, 0, 180),
                      ),
                      padding: MaterialStateProperty.all<EdgeInsetsGeometry>(
                        EdgeInsets.symmetric(
                            vertical: screenHeight * 0.01,
                            horizontal: screenWidth *
                                0.03), // 1% of screen height, 3% of screen width
                      ),
                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                              screenWidth * 0.01), // 1% of screen width
                        ),
                      ),
                    ),
                    child: const Text(
                      'Join Contest',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildAvatarRow(double screenWidth, String numberOfUsersJoined) {
    final List<String> imageUrls = [
      'https://via.placeholder.com/150',
      'https://via.placeholder.com/150',
      'https://via.placeholder.com/150',
      'https://via.placeholder.com/150'
    ];

    final double overlap = screenWidth * 0.03; // 0.5% of screen width
    const int maxAvatars = 4;

    List<Widget> avatarWidgets = [];

    // Determine the number of avatars to display, up to the maximum
    int avatarCount = int.parse(numberOfUsersJoined);
    if (avatarCount > maxAvatars) {
      avatarCount = maxAvatars;
    }

    // Add avatars to the list
    for (int i = 0; i < avatarCount; i++) {
      final double leftPosition = -overlap * i;
      avatarWidgets.add(
        Transform.translate(
          offset: Offset(leftPosition, 0),
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white, // Change the color of the border as needed
                width: 2.0, // Adjust the width of the border as needed
              ),
            ),
            child: CircleAvatar(
              radius: screenWidth * 0.04, // 4% of screen width
              backgroundImage: NetworkImage(imageUrls[i]),
            ),
          ),
        ),
      );
    }

    // Add a text widget displaying the number of remaining users
    if (int.parse(numberOfUsersJoined) > maxAvatars) {
      avatarWidgets.add(
        Transform.translate(
          offset: Offset(
              -overlap * (maxAvatars - 1), 0), // Adjust the offset as needed
          child: Text('+ $numberOfUsersJoined'),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: avatarWidgets,
    );
  }
}
