import 'package:flutter/material.dart';
import 'dart:math' as math;

class ElasticRefreshIndicator extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double displacement;
  final Color? backgroundColor;

  const ElasticRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.displacement = 80.0,
    this.backgroundColor,
  });

  @override
  State<ElasticRefreshIndicator> createState() => _ElasticRefreshIndicatorState();
}

class _ElasticRefreshIndicatorState extends State<ElasticRefreshIndicator>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _stretchController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _stretchAnimation;
  
  bool _isRefreshing = false;
  double _stretchOffset = 0.0;

  @override
  void initState() {
    super.initState();
    
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _stretchController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _stretchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _stretchController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _stretchController.dispose();
    super.dispose();
  }

  Widget _buildCricketBall() {
    return AnimatedBuilder(
      animation: Listenable.merge([_rotationAnimation, _stretchAnimation]),
      builder: (context, child) {
        final scale = 0.7 + (_stretchAnimation.value * 0.3);
        return Transform.scale(
          scale: scale,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: const RadialGradient(
                  colors: [
                    Color(0xFFDC143C), // Crimson red
                    Color(0xFF8B0000), // Dark red
                  ],
                  stops: [0.3, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(2, 2),
                  ),
                ],
              ),
              child: CustomPaint(
                painter: CricketBallPainter(),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        setState(() {
          _isRefreshing = true;
        });
        
        _rotationController.repeat();
        _stretchController.forward().then((_) {
          _stretchController.reverse();
        });
        
        try {
          await widget.onRefresh();
        } finally {
          _rotationController.stop();
          _rotationController.reset();
          setState(() {
            _isRefreshing = false;
          });
        }
      },
      displacement: widget.displacement,
      backgroundColor: Colors.transparent,
      color: Colors.transparent,
      strokeWidth: 0,
      child: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
          if (notification is OverscrollNotification && !_isRefreshing) {
            if (notification.overscroll < 0) {
              // User is pulling down
              setState(() {
                _stretchOffset = notification.overscroll.abs() * 0.5;
              });
              
              final progress = (_stretchOffset / widget.displacement).clamp(0.0, 1.0);
              _stretchController.value = progress;
            }
          } else if (notification is ScrollEndNotification && !_isRefreshing) {
            setState(() {
              _stretchOffset = 0.0;
            });
            _stretchController.reverse();
          }
          return false;
        },
        child: Stack(
          children: [
            // Background stretch effect
            AnimatedBuilder(
              animation: _stretchAnimation,
              builder: (context, child) {
                return Container(
                  color: (widget.backgroundColor ?? Colors.grey.shade50)
                      .withValues(alpha: _stretchAnimation.value * 0.3),
                );
              },
            ),
            
            // Main content with elastic transform
            AnimatedBuilder(
              animation: _stretchAnimation,
              builder: (context, child) {
                final translateY = _stretchOffset * 0.6; // Elastic damping
                return Transform.translate(
                  offset: Offset(0, translateY),
                  child: widget.child,
                );
              },
            ),
            
            // Cricket ball indicator
            if (_stretchOffset > 10 || _isRefreshing)
              AnimatedBuilder(
                animation: _stretchAnimation,
                builder: (context, child) {
                  final opacity = _stretchAnimation.value;
                  final ballY = (_stretchOffset * 0.4) - 20;
                  
                  return Positioned(
                    top: math.max(10, ballY),
                    left: MediaQuery.of(context).size.width / 2 - 20,
                    child: Opacity(
                      opacity: opacity.clamp(0.0, 1.0),
                      child: _buildCricketBall(),
                    ),
                  );
                },
              ),
            
            // Pull instruction text
            if (_stretchOffset > 20 && !_isRefreshing)
              AnimatedBuilder(
                animation: _stretchAnimation,
                builder: (context, child) {
                  final opacity = (_stretchAnimation.value * 1.2).clamp(0.0, 1.0);
                  final textY = (_stretchOffset * 0.4) + 25;
                  
                  return Positioned(
                    top: math.max(40, textY),
                    left: 0,
                    right: 0,
                    child: Opacity(
                      opacity: opacity,
                      child: Center(
                        child: Text(
                          _stretchOffset >= widget.displacement
                              ? 'Release to refresh'
                              : 'Pull down to refresh',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}

class CricketBallPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw the seam (curved line across the ball)
    final path = Path();
    
    // Left curve
    path.moveTo(center.dx - radius * 0.8, center.dy - radius * 0.3);
    path.quadraticBezierTo(
      center.dx - radius * 0.3, center.dy - radius * 0.1,
      center.dx, center.dy,
    );
    
    // Right curve
    path.quadraticBezierTo(
      center.dx + radius * 0.3, center.dy + radius * 0.1,
      center.dx + radius * 0.8, center.dy + radius * 0.3,
    );
    
    canvas.drawPath(path, paint);
    
    // Draw stitching marks
    final stitchPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;
    
    // Left side stitches
    for (int i = 0; i < 6; i++) {
      final t = i / 5.0;
      final x = center.dx - radius * 0.6 + (radius * 0.6 * t);
      final y = center.dy - radius * 0.2 + (radius * 0.4 * t);
      
      canvas.drawLine(
        Offset(x - 3, y - 1),
        Offset(x + 3, y + 1),
        stitchPaint,
      );
    }
    
    // Right side stitches
    for (int i = 0; i < 6; i++) {
      final t = i / 5.0;
      final x = center.dx + (radius * 0.6 * t);
      final y = center.dy - radius * 0.1 + (radius * 0.2 * t);
      
      canvas.drawLine(
        Offset(x - 3, y - 1),
        Offset(x + 3, y + 1),
        stitchPaint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
