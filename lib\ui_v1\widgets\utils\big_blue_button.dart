import 'package:flutter/material.dart';

class BigBlueButton extends StatefulWidget {
  final IconData? icon;
  final String text;
  final VoidCallback onPressed;
  final Widget? onSplitPressed;

  const BigBlueButton({
    super.key,
    required this.icon,
    required this.text,
    required this.onPressed,
    this.onSplitPressed,
  });

  @override
  State<BigBlueButton> createState() => _BigBlueButtonState();
}

class _BigBlueButtonState extends State<BigBlueButton> {
  bool _isSplitActionVisible = false; // Track visibility of split action text

  void _toggleSplitAction() {
    setState(() {
      _isSplitActionVisible = !_isSplitActionVisible;
    });
  }

  /// Build the content of the button
  Widget _buildButtonContent() {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;

    // Calculate responsive values based on screen size
    double iconSize = screenWidth * 0.05; // Icon size based on screen width
    double textSize = screenWidth * 0.04; // Text size based on screen width
    double padding = screenWidth * 0.04; // Padding based on screen width

    return Row(
      children: [
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (widget.icon != null)
                Icon(widget.icon, color: Colors.white, size: iconSize),
              if (widget.icon != null) SizedBox(width: padding),
              Text(
                widget.text,
                style: TextStyle(color: Colors.white, fontSize: textSize),
              ),
            ],
          ),
        ),
        if (widget.onSplitPressed != null && widget.onSplitPressed is! SizedBox)
          InkWell(
            onTap: _toggleSplitAction,
            child: Container(
              height: screenHeight * 0.03, // Height based on screen height
              width: screenWidth * 0.12, // Width based on screen width
              alignment: Alignment.center,
              child: Icon(
                _isSplitActionVisible ? Icons.arrow_drop_up : Icons.arrow_right,
                color: Colors.white,
                size: iconSize,
              ),
            ),
          )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double padding = screenWidth * 0.02; // Padding based on screen width

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(1),
          child: ElevatedButton(
            onPressed: widget.onPressed,
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.all(padding),
              backgroundColor: const Color.fromARGB(255, 48, 0, 180),
            ),
            child: Padding(
              padding: EdgeInsets.all(padding),
              child: _buildButtonContent(),
            ),
          ),
        ),
        if (_isSplitActionVisible) widget.onSplitPressed!
      ],
    );
  }
}
