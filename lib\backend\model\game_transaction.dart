import 'base_transaction.dart';
import 'match.dart';

class GameTransaction extends BaseTransaction {
  final String contestId;
  final Match match;

  GameTransaction({
    required super.userId,
    required super.timeStamp,
    required super.transactionAmount,
    required super.paymentType,
    required this.match,
    required this.contestId,
  });

  factory GameTransaction.fromJson(Map<String, dynamic> json) {
    return GameTransaction(
      userId: json['userId'],
      timeStamp: json['timeStamp'],
      transactionAmount: json['amount'],
      paymentType: json['paymentType'],
      match: Match.fromJson(json['match']),
      contestId: json['contestId'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'timeStamp': timeStamp,
      'amount': amount,
      'paymentType': paymentType,
      'match': match.toJson(),
      'contestId': contestId,
    };
  }
}
