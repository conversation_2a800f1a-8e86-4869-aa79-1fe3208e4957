import 'package:shared_preferences/shared_preferences.dart';

/// Provides persistent storage for theme preferences.
///
/// <PERSON><PERSON> saving and retrieving the user's theme mode preference.
class ThemeService {
  /// Key used to store theme mode in SharedPreferences
  static const String _themeKey = 'theme_mode';

  /// Saves the user's theme mode preference
  ///
  /// @param isDarkMode True if dark mode is enabled, false for light mode
  static Future<void> saveThemeMode(bool isDarkMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_themeKey, isDarkMode);
  }

  /// Retrieves the user's theme mode preference
  ///
  /// @return True if dark mode is enabled, false for light mode
  /// Default is light mode (false) if no preference is set
  static Future<bool> getThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_themeKey) ?? false;
  }
}
