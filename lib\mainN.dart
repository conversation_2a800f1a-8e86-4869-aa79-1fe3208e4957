import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'package:top3/ui_v1/utils/user_context.dart';
import 'auth/auth_details.dart';
import 'core/utils/logger.dart';
import 'ui_v1/routes/routes.dart';
import 'ui_v1/themes/dark_theme.dart';
import 'ui_v1/utils/notification_helper.dart';
import 'injection_container.dart' as di;

import 'ui_v1/utils/constants.dart';
import 'ui_v1/utils/context.dart';
import 'firebase_options.dart';

import 'ui_v1/themes/light_theme.dart';
import 'ui_v1/utils/theme_provider.dart';

void main() async {
  try {
    // Initialize Flutter binding
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize logger
    Logger.init(
      logLevel: kDebugMode ? LogLevel.debug : LogLevel.info,
      logToConsole: true,
    );

    // Initialize Firebase
    await Firebase.initializeApp(
      name: "Top3",
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Initialize notifications
    await NotificationHelper.initialize();

    // Initialize dependency injection
    await di.init();

    Logger.info('App initialized successfully');

    // Run the app
    runApp(
      ChangeNotifierProvider(
        create: (_) => ThemeProvider(),
        child: const Application(),
      ),
    );
  } catch (e) {
    Logger.error('Failed to initialize app', error: e);
    // Show error UI or rethrow based on your error handling strategy
    rethrow;
  }
}

/// Main application widget
///
/// Handles initialization and authentication state
class Application extends StatefulWidget {
  /// Creates a new Application instance
  const Application({super.key});

  @override
  State<Application> createState() => ApplicationState();
}

/// State for the Application widget
class ApplicationState extends State<Application> {
  /// Future that resolves to the user's login status
  late Future<bool?> _loggedInFuture;

  @override
  void initState() {
    super.initState();
    _loggedInFuture = _initializeApp();
  }

  /// Initializes the application based on authentication state
  ///
  /// Loads user context and caches match data if the user is logged in
  Future<bool?> _initializeApp() async {
    try {
      bool? isLoggedIn = await AuthDetails.getIfLoggedIn();
      if (isLoggedIn == true) {
        Logger.info('User is logged in, initializing user context');
        await UserContext.init();
        await Context.fetchAndCacheMatches();
      } else {
        Logger.info('User is not logged in');
      }
      return isLoggedIn;
    } catch (e, stackTrace) {
      Logger.error('Initialization error', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool?>(
      future: _loggedInFuture,
      builder: (context, snapshot) {
        Widget child;

        if (snapshot.connectionState == ConnectionState.waiting) {
          // Show loading indicator while initializing
          child = const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else if (snapshot.hasError) {
          // Show error message if initialization fails
          child = Scaffold(
            body: Center(
              child: Text(
                'Error during initialization. Please restart the app.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ),
          );
        } else {
          // Show main app if initialization succeeds
          final bool loggedInStatus = snapshot.data ?? false;
          child = MaterialApp(
            title: 'Top3',
            theme: LightTheme().buildThemeData(),
            darkTheme: DarkTheme().buildThemeData(),
            themeMode: Provider.of<ThemeProvider>(context).themeMode,
            initialRoute: loggedInStatus
                ? RoutePaths.main
                : RoutePaths.googleSignInScreen,
            onGenerateRoute: Routes.generateRoute,
          );
        }

        // Wrap in PopScope to handle back button presses
        return MaterialApp(
          home: PopScope(
            canPop: false,
            child: child,
          ),
        );
      },
    );
  }
}
