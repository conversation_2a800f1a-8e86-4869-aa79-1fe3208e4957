{"buildFiles": ["D:\\FLUTTER\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Git\\top3_v1\\android\\app\\.cxx\\RelWithDebInfo\\3t2i5r66\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Git\\top3_v1\\android\\app\\.cxx\\RelWithDebInfo\\3t2i5r66\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}