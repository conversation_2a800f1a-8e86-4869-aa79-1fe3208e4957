import 'package:flutter/material.dart';
import '../../backend/model/player.dart';
import '../../backend/model/match.dart';
import '../utils/image_helper.dart';

typedef Top3ListCallback = void Function(List<Player>);

class PlayerSelectionWidget extends StatefulWidget {
  final List<Player> teamAPlayers;
  final List<Player> teamBPlayers;
  final Match? match;
  final List<Player> top3List;
  final List<IconData> iconList;
  final bool isAnnounced;
  final Top3ListCallback onTop3ListChanged;

  const PlayerSelectionWidget({
    super.key,
    required this.teamAPlayers,
    required this.teamBPlayers,
    required this.match,
    required this.top3List,
    required this.iconList,
    required this.isAnnounced,
    required this.onTop3ListChanged,
  });

  @override
  _PlayerSelectionWidgetState createState() => _PlayerSelectionWidgetState();
}

class _PlayerSelectionWidgetState extends State<PlayerSelectionWidget> {
  late String selectedTeam;
  late String team1;
  late String team2;

  @override
  void initState() {
    super.initState();
    selectedTeam = widget.match!.team1;
    team1 = widget.match!.team1;
    team2 = widget.match!.team2;
  }

  @override
  Widget build(BuildContext context) {
    return _buildTeamPlayersSection(context);
  }

  Widget _buildTeamPlayersSection(BuildContext context) {
    List<Player> selectedTeamPlayers =
        selectedTeam == team1 ? widget.teamAPlayers : widget.teamBPlayers;
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;
    double availableHeight = screenHeight * 0.42;
    double horizontalPadding = screenWidth * 0.04;
    double avatarRadius = screenWidth * 0.06;

    return Card(
      margin: EdgeInsets.all(horizontalPadding),
      child: Column(
        children: [
          SizedBox(height: screenHeight * 0.02),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              SizedBox(width: screenWidth * 0.1),
              _buildTeamHeader(team1, screenWidth),
              const Spacer(),
              _buildTeamHeader(team2, screenWidth),
              SizedBox(width: screenWidth * 0.1),
            ],
          ),
          const Divider(),
          SizedBox(
            height: availableHeight,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: selectedTeamPlayers.map((player) {
                  int index = widget.top3List
                      .indexWhere((element) => element.name == player.name);
                  bool isSelected = index != -1;
                  Color highlightedColor = isSelected
                      ? Colors.orange.withOpacity(0.2)
                      : widget.isAnnounced
                          ? (player.status ?? true)
                              ? Colors.transparent
                              : Colors.grey.withOpacity(0.2)
                          : Colors.transparent;
                  return IgnorePointer(
                    ignoring: isSelected,
                    child: GestureDetector(
                      onTap: () {
                        int availableIndex = widget.top3List
                            .indexWhere((element) => element.name == "name");
                        if (availableIndex != -1) {
                          setState(() {
                            widget.top3List[availableIndex] = player;
                            widget.iconList[availableIndex] = Icons.person_2;
                            widget.onTop3ListChanged(widget.top3List);
                          });
                        }
                      },
                      child: Container(
                        margin: EdgeInsets.symmetric(
                            vertical: screenHeight * 0.003,
                            horizontal: horizontalPadding),
                        decoration: BoxDecoration(
                          color: highlightedColor,
                          border: Border.all(
                            color: highlightedColor,
                            width: 2.0,
                          ),
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.02),
                        ),
                        child: ListTile(
                          leading: ImageHelper.getCircleAvatar(
                            imageUrl: ImageHelper.playerImageBaseUrl + player.imagePath!,
                            radius: avatarRadius,
                          ),
                          title: Text(
                            player.name!,
                            style: TextStyle(fontSize: screenWidth * 0.035),
                          ),
                          trailing: isSelected
                              ? Container(
                                  width: screenWidth * 0.08,
                                  height: screenWidth * 0.08,
                                  decoration: const BoxDecoration(
                                    color: Colors.orange,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Text(
                                      (index + 1).toString(),
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: screenWidth * 0.035),
                                    ),
                                  ),
                                )
                              : null,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTeamHeader(String teamName, double screenWidth) {
    Color textColor = selectedTeam == teamName
        ? const Color.fromARGB(255, 48, 0, 180)
        : Colors.black;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedTeam = teamName;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.08),
        child: Text(
          teamName,
          style: TextStyle(
            fontSize: screenWidth * 0.05,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
      ),
    );
  }
}
