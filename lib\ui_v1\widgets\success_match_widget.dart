import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class SuccessMatchWidget extends StatelessWidget {
  final String team1;
  final String team2;
  final String team1Flag;
  final String team2Flag;
  final String date;

  const SuccessMatchWidget({super.key, 
    required this.team1,
    required this.team2,
    required this.team1Flag,
    required this.team2Flag,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            CircleAvatar(
              radius: 28,
              backgroundImage: NetworkImage(
                  "https://top3-images.s3.ap-south-1.amazonaws.com/cricket/flags/$team1Flag"),
            ),
            Text(
              team1,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Text(
              'V / S',
              style: TextStyle(
                  fontSize: 25,
                  color: Colors.orange,
                  fontWeight: FontWeight.bold),
            ),
            Text(
              team2,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            CircleAvatar(
              radius: 28,
              backgroundImage: NetworkImage(
                  "https://top3-images.s3.ap-south-1.amazonaws.com/cricket/flags/$team2Flag"),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.calendar_today, color: Colors.orange),
            const SizedBox(width: 8),
            Text(
              DateFormat('yyyy-MM-dd')
                  .format(DateTime.fromMillisecondsSinceEpoch(int.parse(date))),
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ],
    );
  }
}
