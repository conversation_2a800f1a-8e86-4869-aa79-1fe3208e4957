class Player {
  String? name;
  String? playerId;
  String? imagePath;
  String? teamId;
  bool? status;
  String? points;

  Player({
    required this.name,
    required this.playerId,
    required this.imagePath,
    required this.teamId,
    required this.status,
    this.points,
  });

  factory Player.fromMap(Map<String, dynamic> map) {
    return Player(
      name: map['name'] as String?,
      playerId: map['playerId'] as String?,
      imagePath: map['imagePath'] as String?,
      teamId: map['teamId'] as String?,
      status: map['status'] as bool?,
      points: map['points'] as String?,
    );
  }
}
