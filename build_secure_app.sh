#!/bin/bash
echo "Building secure app with obfuscation enabled..."

echo "Cleaning the build..."
flutter clean
if [ $? -ne 0 ]; then
    echo "Error during flutter clean"
    exit 1
fi

echo "Getting dependencies..."
flutter pub get
if [ $? -ne 0 ]; then
    echo "Error during flutter pub get"
    exit 1
fi

echo "Encrypting configuration file..."
dart encrypt_config.dart
if [ $? -ne 0 ]; then
    echo "Error during configuration encryption"
    exit 1
fi

echo "Building Android APK with obfuscation..."
flutter build apk --obfuscate --split-debug-info=build/debug-info --release
if [ $? -ne 0 ]; then
    echo "Error during flutter build"
    exit 1
fi

# Uncomment for iOS build
# echo "Building iOS app with obfuscation..."
# flutter build ios --obfuscate --split-debug-info=build/debug-info --release
# if [ $? -ne 0 ]; then
#     echo "Error during iOS build"
#     exit 1
# fi

echo "Build completed successfully!"
echo "The obfuscated APK is located at: build/app/outputs/flutter-apk/app-release.apk"

echo "Running the app..."
flutter run --release
if [ $? -ne 0 ]; then
    echo "Error running the app"
    exit 1
fi
