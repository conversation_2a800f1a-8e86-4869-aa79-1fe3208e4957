import 'package:flutter/material.dart';
import '../../../backend/model/player.dart';
import '../../../backend/model/contest.dart';
import '../../../backend/model/match.dart';
import '../../../backend/rest/match_details.dart';
import '../../widgets/cards/joined_contest_card.dart';
import '../../utils/user_context.dart';
import '../../widgets/utils/match_status_widget.dart';
import '../../widgets/utils/timer/match_countdown_timer.dart';

class JoinedContests extends StatefulWidget {
  final indianRupeeSymbol = '\u20B9';
  final Match? match;
  final Map<String, dynamic>? map;

  const JoinedContests(this.match, this.map, {super.key});

  @override
  _JoinedContestsState createState() => _JoinedContestsState();
}

class _JoinedContestsState extends State<JoinedContests> {
  late Duration remainingTime;
  late MatchCountdownTimer timer;
  late Map numberOfUsersJoined;
  late List otherEntries;
  late Map<String, dynamic>? map;
  bool _isTimeInitialized = false;

  @override
  void initState() {
    super.initState();
    map = widget.map;
    init();
  }

  void init() {
    numberOfUsersJoined = map!.remove('matchContestDetails');
    otherEntries = map!.entries.toList();
    Match match = widget.match!;
    remainingTime = Duration.zero;
    timer = MatchCountdownTimer(
        targetDateTime: match.startDate,
        onTick: (Duration remainingTime) {
          setState(() {
            this.remainingTime = remainingTime;
            _isTimeInitialized = true;
          });
        },
        onEnd: () async {
          Match updatedMatch =
              await MatchDetails.getMatchDetails(match.matchId);
          setState(() {
            match = updatedMatch;
          });
        });
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    if (!_isTimeInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "Joined Contests",
          style: TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshContests, // Method to refresh the data
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    widget.match!.team1,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 48, 0, 180),
                    ),
                  ),
                  const Text(
                    " vs ",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 48, 0, 180),
                    ),
                  ),
                  Text(
                    widget.match!.team2,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 48, 0, 180),
                    ),
                  ),
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                MatchStatusWidget(
                  matchState: widget.match!.matchState,
                  startDate: widget.match!.startDate,
                  remainingTime: remainingTime,
                ),
              ],
            ),
            Expanded(
              child: ListView.builder(
                itemCount: otherEntries.length,
                itemBuilder: (BuildContext context, int index) {
                  final entry = otherEntries[index];
                  final key = entry.key;
                  final int entryFee =
                      int.parse(key.toString().substring(0, 2));
                  final value = entry.value;

                  final int total = entryFee *
                      numberOfUsersJoined[entryFee.toString()] as int;
                  final int pool = (total * 0.8).round();
                  final int firstPrize = (total * 0.5).round();
                  final int secondPrize = (total * 0.3).round();

                  List<Player> top3List = [
                    Player.fromMap(value["top1"]),
                    Player.fromMap(value["top2"]),
                    Player.fromMap(value["top3"])
                  ];

                  return JoinedContestCard(
                    indianRupeeSymbol: widget.indianRupeeSymbol,
                    screenWidth: screenWidth,
                    screenHeight: screenHeight,
                    pool: pool.toString(),
                    firstPrize: firstPrize.toString(),
                    secondPrize: secondPrize.toString(),
                    top3List: top3List,
                    saved: true,
                    onTop3ListChanged: (List<Player> a) {},
                    match: widget.match,
                    contest: Contest(key, entryFee.toString(),
                        numberOfUsersJoined[entryFee.toString()].toString()),
                    remainingTime: remainingTime,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _refreshContests() async {
    map = await MatchDetails.getUserJoinedMatchContestDetails(
      UserContext.userId,
      widget.match!.matchId,
    );
    setState(() {
      init();
    });
  }
}
