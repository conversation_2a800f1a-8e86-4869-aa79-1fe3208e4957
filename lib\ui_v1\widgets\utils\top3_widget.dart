import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../backend/model/player.dart';
import '../../utils/image_helper.dart';

typedef Top3ListCallback = void Function(List<Player>);

class Top3Widget extends StatefulWidget {
  final List<Player> top3List;
  final bool saved;
  final bool showPoints;
  final Top3ListCallback onTop3ListChanged;

  const Top3Widget(
      {super.key,
      required this.top3List,
      required this.saved,
      required this.showPoints,
      required this.onTop3ListChanged});

  @override
  _Top3WidgetState createState() => _Top3WidgetState();
}

class _Top3WidgetState extends State<Top3Widget> {
  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    final double avatarSize = screenHeight * 0.095;
    final double iconSize = avatarSize * 0.3;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
      child: Column(
        children: [
          _buildGraphIconsRow(screenHeight, screenWidth),
          SizedBox(height: screenHeight * 0.01),
          _buildAvatarHoldersRow(avatarSize, iconSize),
          SizedBox(height: screenHeight * 0.01),
          _buildNamesRow(screenWidth * 0.035),
          if (widget.saved) _buildTop3TagRow(screenWidth),
          if (widget.showPoints) _buildPointsRow()
        ],
      ),
    );
  }

  Widget _buildGraphIconsRow(double screenHeight, double screenwidth) {
    final double barMaxHeight = screenHeight * 0.03;
    final double barMaxWidth = screenwidth * 0.02;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildGraphIcon(0, barMaxHeight, barMaxWidth),
        _buildGraphIcon(1, barMaxHeight, barMaxWidth),
        _buildGraphIcon(2, barMaxHeight, barMaxWidth),
      ],
    );
  }

  Widget _buildAvatarHoldersRow(double avatarSize, double iconSize) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildAvatarHolder(widget.top3List[0].imagePath!, 0, avatarSize, iconSize),
        _buildAvatarHolder(widget.top3List[1].imagePath!, 1, avatarSize, iconSize),
        _buildAvatarHolder(widget.top3List[2].imagePath!, 2, avatarSize, iconSize),
      ],
    );
  }

  Widget _buildNamesRow(double screenWidth) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildName(widget.top3List[0].name!, screenWidth),
        _buildName(widget.top3List[1].name!, screenWidth),
        _buildName(widget.top3List[2].name!, screenWidth),
      ],
    );
  }

  Widget _buildName(String name, double screenWidth) {
    return Expanded(
      child: Column(
        children: [
          Text(
            name,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: screenWidth),
          ),
        ],
      ),
    );
  }

  Widget _buildGraphIcon(int selectedIndex, double maxHeight, double width) {
    Color baseColor = Colors.grey;
    Color highlightColor = const Color.fromARGB(255, 48, 0, 180);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        _buildBar(maxHeight * 0.8, width,
            selectedIndex == 0 ? highlightColor : baseColor),
        const SizedBox(width: 1),
        _buildBar(maxHeight * 0.6, width,
            selectedIndex == 1 ? highlightColor : baseColor),
        const SizedBox(width: 1),
        _buildBar(maxHeight * 0.4, width,
            selectedIndex == 2 ? highlightColor : baseColor),
      ],
    );
  }

  Widget _buildBar(double height, double width, Color color) {
    return Column(
      children: [
        if (color == const Color.fromARGB(255, 48, 0, 180))
          Icon(Icons.star, color: Colors.amber, size: width * 1.7),
        Container(
          width: width,
          height: height,
          color: color,
          alignment: Alignment.bottomCenter,
        ),
      ],
    );
  }

  Widget _buildAvatarHolder(
      String imagePath, int index, double avatarSize, double iconSize) {
    bool showCancelIcon = imagePath != "imagePath" && !widget.saved;
    final String imageUrl = ImageHelper.playerImageBaseUrl + imagePath;

    return Container(
      width: avatarSize,
      height: avatarSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: avatarSize * 0.07,
        ),
      ),
      child: Stack(
        children: [
          CachedNetworkImage(
            imageUrl: imageUrl,
            imageBuilder: (context, imageProvider) => CircleAvatar(
              radius: avatarSize * 0.5,
              backgroundColor: const Color(0xFFF4F4F4),
              backgroundImage: imageProvider,
            ),
            placeholder: (context, url) => CircleAvatar(
              radius: avatarSize * 0.5,
              backgroundColor: const Color(0xFFF4F4F4),
              child: const CircularProgressIndicator(),
            ),
            errorWidget: (context, url, error) => CircleAvatar(
              radius: avatarSize * 0.5,
              backgroundColor: const Color(0xFFF4F4F4),
              child: const Icon(Icons.error),
            ),
          ),
          if (showCancelIcon)
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    widget.top3List[index] = Player(
                        name: "name",
                        playerId: "playerId",
                        imagePath: "imagePath",
                        teamId: "teamId",
                        status: false);
                    widget.onTop3ListChanged(widget.top3List);
                  });
                },
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.cancel,
                      color: const Color.fromARGB(255, 216, 216, 216),
                      size: iconSize),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTop3TagRow(double screenWidth) {
    return Column(
      children: [
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            buildTopContainer('Top 1', screenWidth),
            buildTopContainer('Top 2', screenWidth),
            buildTopContainer('Top 3', screenWidth),
          ],
        )
      ],
    );
  }

  Widget buildTopContainer(String text, double screenWidth) {
    return Container(
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 221, 221, 255),
          borderRadius: BorderRadius.circular(5),
        ),
        padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.02, vertical: screenWidth * 0.01),
        child: buildText(text));
  }

  Widget _buildPointsRow() {
    return Column(
      children: [
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            buildText(widget.top3List[0].points.toString()),
            buildText(widget.top3List[1].points.toString()),
            buildText(widget.top3List[2].points.toString()),
          ],
        )
      ],
    );
  }

  Widget buildText(String text) {
    return Text(
      text,
      style: const TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.bold,
        color: Color.fromARGB(255, 48, 0, 180),
      ),
    );
  }
}
