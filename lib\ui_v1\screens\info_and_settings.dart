import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../ui_v1/themes/app_colors.dart';
import '../../auth/auth_details.dart';
import '../../backend/rest/user_details.dart';
import '../../ui_v1/utils/constants.dart';
import '../../ui_v1/utils/context.dart';
import '../utils/user_context.dart';
import '../widgets/utils/big_blue_button.dart';

class InfoAndSettings extends StatefulWidget {
  final String userName;
  final String userId;
  final String userEmail;
  final bool existingUser;
  final String? photoUrl;

  const InfoAndSettings({
    super.key,
    required this.userName,
    required this.userId,
    required this.userEmail,
    required this.existingUser,
    this.photoUrl,
  });

  @override
  _InfoAndSettingsState createState() => _InfoAndSettingsState();
}

class _InfoAndSettingsState extends State<InfoAndSettings> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _idController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  String? _selectedGender;

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.userName;
    _emailController.text = widget.userEmail;
    _idController.text = widget.userId;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("My Info & Settings"),
        actions: widget.existingUser
            ? <Widget>[
                PopupMenuButton<int>(
                  offset: const Offset(0, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  color: AppColors.of(context).cardBackground,
                  itemBuilder: (context) => [
                    PopupMenuItem<int>(
                      value: 0,
                      child: Row(
                        children: const [
                          Icon(Icons.delete_outline_outlined,
                              color: Colors.red),
                          SizedBox(width: 8),
                          Text("Delete Account",
                              style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    if (value == 0) {
                      print("Delete Account Clicked");
                    }
                  },
                  icon: const Icon(Icons.more_vert_outlined),
                ),
              ]
            : null,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTextField(
                label: widget.userName,
                hint: widget.userName,
                controller: _nameController,
                isReadOnly: false,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter Name';
                  }
                  if (!RegExp(r'^[a-zA-Z ]+$').hasMatch(value)) {
                    return 'Name can only contain letters and spaces';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 15),
              _buildTextField(
                label: widget.userId,
                hint: widget.userId,
                controller: _idController,
                isReadOnly: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter User id';
                  }
                  if (value.length != 28) {
                    return 'Username must be 28 characters';
                  }
                  if (!RegExp(r'^[a-zA-Z0-9_.]+$').hasMatch(value)) {
                    return 'Only letters, numbers, _ and . allowed';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 15),
              _buildTextField(
                label: widget.userEmail,
                hint: widget.userEmail,
                controller: _emailController,
                isReadOnly: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter Email';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                      .hasMatch(value)) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 15),
              _buildTextField(
                label: "Mobile Number",
                hint: "Enter mobile number",
                isReadOnly: false,
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter Mobile Number';
                  }
                  if (!RegExp(r'^[0-9]{10}$').hasMatch(value)) {
                    return 'Mobile number must be 10 digits';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 15),
              _buildTextField(
                label: "Date of Birth",
                hint: "Select Date of Birth",
                isReadOnly: true,
                controller: _dobController,
                suffixIcon: IconButton(
                  icon: const Icon(Icons.calendar_today,
                      color: Colors.deepPurple),
                  onPressed: () => _selectDate(context),
                ),
              ),
              const SizedBox(height: 15),
              _buildGenderDropdown(),
              const SizedBox(height: 30),
              Center(
                child: BigBlueButton(
                  icon: null,
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      await UserDetails.registerUser(
                        widget.userId,
                        widget.userEmail,
                        _nameController.text.trim(),
                        widget.photoUrl ?? '',
                      );
                      // ScaffoldMessenger.of(context).showSnackBar(
                      //   const SnackBar(
                      //       content: Text('Details saved successfully!')),
                      // );
                      AuthDetails.setIfLoggedIn(true);
                      AuthDetails.setUserId(widget.userId);
                      AuthDetails.setUserEmail(widget.userEmail);
                      AuthDetails.setUserName(_nameController.text.trim());
                      AuthDetails.setUserPhotoUrl(widget.photoUrl ?? '');
                      final userBalance =
                          await UserDetails.getUserDetails(widget.userId);
                      AuthDetails.setUserBalance(userBalance);

                      await UserContext.init();
                      await Context.fetchAndCacheMatches();
                      Navigator.pushNamed(context, RoutePaths.main);
                    }
                  },
                  text: "Save Details",
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required String hint,
    required bool isReadOnly,
    TextEditingController? controller,
    TextInputType keyboardType = TextInputType.text,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        suffixIcon: suffixIcon,
      ),
      keyboardType: keyboardType,
      readOnly: isReadOnly,
      validator: validator ??
          (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter $label';
            }
            return null;
          },
    );
  }

  Widget _buildGenderDropdown() {
    return DropdownButtonFormField<String>(
      decoration: const InputDecoration(
        labelText: "Gender",
        border: OutlineInputBorder(),
      ),
      value: _selectedGender,
      hint: const Text("Select your gender"),
      validator: (value) => value == null ? 'Please select your gender' : null,
      onChanged: (String? newValue) {
        setState(() {
          _selectedGender = newValue;
        });
      },
      items: ["Male", "Female", "Other"].map((String gender) {
        return DropdownMenuItem<String>(
          value: gender,
          child: Text(gender, overflow: TextOverflow.ellipsis),
        );
      }).toList(),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _dobController.text = "${picked.toLocal()}".split(' ')[0];
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _mobileController.dispose();
    _dobController.dispose();
    super.dispose();
  }
}
