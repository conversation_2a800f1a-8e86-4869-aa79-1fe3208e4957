# Top3 Application Flow Diagrams

This document outlines all the possible flows in the Top3 fantasy sports application, including routing logic, security features, and user interactions.

## 1. Application Initialization Flow

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │     │                 │
│  App Launch     │────▶│ Flutter Binding │────▶│ Logger Init     │────▶│ Security Checks │
│                 │     │ Initialization  │     │                 │     │ (Secure Mode)   │
└─────────────────┘     └─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                                                  │
                                                                                  ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │     │                 │
│ Run Application │◀────│ Dependency      │◀────│ Notification    │◀────│ Firebase        │
│                 │     │ Injection       │     │ Initialization  │     │ Initialization  │
└─────────────────┘     └─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Differences in Secure Mode:
- In `main_secure.dart`, additional security checks are performed:
  - Root/jailbreak detection
  - Emulator detection
  - App tampering detection
- Secure configuration loading with obfuscation

## 2. Authentication Flow

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│ App Start       │────▶│ Check Login     │────▶│ Not Logged In   │
│                 │     │ Status          │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
                                               ┌─────────────────────┐
                                               │                     │
                                               │ Google Sign-In      │
                                               │ Screen              │
                                               │                     │
                                               └──────────┬──────────┘
                                                          │
                                                          ▼
                         ┌───────────────────────────────────────────────────┐
                         │                                                   │
                         ▼                                                   ▼
               ┌─────────────────┐                                 ┌─────────────────┐
               │                 │                                 │                 │
               │ Google Auth     │                                 │ Phone Auth      │
               │ Flow            │                                 │ Flow            │
               │                 │                                 │                 │
               └────────┬────────┘                                 └────────┬────────┘
                        │                                                   │
                        ▼                                                   ▼
               ┌─────────────────┐                                 ┌─────────────────┐
               │                 │                                 │                 │
               │ Firebase Auth   │                                 │ OTP             │
               │                 │                                 │ Verification    │
               └────────┬────────┘                                 └────────┬────────┘
                        │                                                   │
                        └───────────────────────┬───────────────────────────┘
                                                │
                                                ▼
                                      ┌─────────────────┐
                                      │                 │
                                      │ Check if User   │
                                      │ Exists          │
                                      │                 │
                                      └────────┬────────┘
                                               │
                        ┌──────────────────────┴──────────────────────┐
                        │                                             │
                        ▼                                             ▼
              ┌─────────────────┐                           ┌─────────────────┐
              │                 │                           │                 │
              │ Existing User   │                           │ New User        │
              │                 │                           │                 │
              └────────┬────────┘                           └────────┬────────┘
                       │                                             │
                       ▼                                             ▼
              ┌─────────────────┐                           ┌─────────────────┐
              │                 │                           │                 │
              │ Initialize      │                           │ Registration    │
              │ User Context    │                           │ Screen          │
              │                 │                           │                 │
              └────────┬────────┘                           └────────┬────────┘
                       │                                             │
                       └─────────────────┬─────────────────┬─────────┘
                                         │                 │
                                         ▼                 │
                               ┌─────────────────┐         │
                               │                 │         │
                               │ Main Screen     │◀────────┘
                               │                 │
                               └─────────────────┘
```

## 3. Main Application Navigation Flow

```
┌─────────────────┐
│                 │
│ Main Screen     │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                      Bottom Navigation Bar                      │
│                                                                 │
└─────────┬─────────────────────────┬─────────────────────┬───────┘
          │                         │                     │
          ▼                         ▼                     ▼
┌─────────────────┐       ┌─────────────────┐   ┌─────────────────┐
│                 │       │                 │   │                 │
│ Home Screen     │       │ My Matches      │   │ Search Screen   │
│                 │       │ Screen          │   │                 │
└────────┬────────┘       └────────┬────────┘   └────────┬────────┘
         │                         │                     │
         ▼                         ▼                     ▼
┌─────────────────┐       ┌─────────────────┐   ┌─────────────────┐
│ Joined Matches  │       │ Tab Bar         │   │ Search Users    │
│ Section         │       │                 │   │                 │
└────────┬────────┘       └────────┬────────┘   └────────┬────────┘
         │                         │                     │
         ▼                         │                     ▼
┌─────────────────┐                │            ┌─────────────────┐
│ Upcoming        │                │            │ User Profiles   │
│ Matches Section │                │            │                 │
└────────┬────────┘                │            └─────────────────┘
         │                         │
         │                         ▼
         │               ┌───────────────────────────────────────┐
         │               │                                       │
         │               │     Live     │ Upcoming │ Completed   │
         │               │                                       │
         │               └───────┬───────────┬─────────┬─────────┘
         │                       │           │         │
         │                       ▼           ▼         ▼
         │               ┌─────────────┐ ┌─────────┐ ┌─────────┐
         │               │ Live        │ │ Upcoming│ │Completed│
         │               │ Matches     │ │ Matches │ │Matches  │
         │               └─────────────┘ └─────────┘ └─────────┘
         │
         └─────────────────────┬─────────────────────┐
                               │                     │
                               ▼                     ▼
                     ┌─────────────────┐   ┌─────────────────┐
                     │                 │   │                 │
                     │ Joined Contests │   │ Show Contests   │
                     │                 │   │                 │
                     └─────────────────┘   └────────┬────────┘
                                                    │
                                                    ▼
                                          ┌─────────────────┐
                                          │                 │
                                          │ Show Contest    │
                                          │                 │
                                          └────────┬────────┘
                                                   │
                                                   ▼
                                          ┌─────────────────┐
                                          │                 │
                                          │ Success Screen  │
                                          │                 │
                                          └─────────────────┘
```

## 4. Side Menu Navigation Flow

```
┌─────────────────┐
│                 │
│ Main Screen     │
│ (Drawer Icon)   │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Side Menu       │
│                 │
└────────┬────────┘
         │
         ▼
┌───────────────────────────────────────────────────────────────────────┐
│                                                                       │
│                        Side Menu Options                              │
│                                                                       │
└────┬────────────┬────────────┬────────────┬────────────┬──────────────┘
     │            │            │            │            │
     ▼            ▼            ▼            ▼            ▼
┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐
│          │ │          │ │          │ │          │ │          │
│ Profile  │ │ Wallet   │ │Transaction│ │Withdrawal│ │Settings  │
│          │ │          │ │          │ │          │ │          │
└────┬─────┘ └────┬─────┘ └────┬─────┘ └────┬─────┘ └────┬─────┘
     │            │            │            │            │
     ▼            ▼            ▼            ▼            ▼
┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐
│Profile   │ │Wallet    │ │Transaction│ │Withdrawal│ │Info and  │
│Screen    │ │Screen    │ │Screen    │ │Screen    │ │Settings  │
└──────────┘ └────┬─────┘ └──────────┘ └──────────┘ └──────────┘
                  │
                  ▼
            ┌──────────┐
            │Payment   │
            │Screen    │
            └──────────┘
```

## 5. Contest Flow

```
┌─────────────────┐
│                 │
│ Upcoming Match  │
│ Card            │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Show Contests   │
│ Screen          │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Contest List    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Show Contest    │
│ Screen          │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Select Top 3    │
│ Players         │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│ Check Balance   │────▶│ Payment Screen  │
│                 │     │ (if needed)     │
└────────┬────────┘     └────────┬────────┘
         │                       │
         │◀──────────────────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Join Contest    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Success Screen  │
│                 │
└─────────────────┘
```

## 6. Payment Flow

```
┌─────────────────┐
│                 │
│ Payment Screen  │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Enter Amount    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Initialize      │
│ Payment SDK     │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Generate Auth   │
│ Token           │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Create Order    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Start           │
│ Transaction     │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Payment Gateway │
│ (PhonePe)       │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Check Status    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────────────────────────┐
│                                     │
▼                                     ▼
┌─────────────────┐           ┌─────────────────┐
│                 │           │                 │
│ Success         │           │ Failure         │
│                 │           │                 │
└────────┬────────┘           └─────────────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Update User     │
│ Balance         │
│                 │
└─────────────────┘
```

## 7. Security Flow (Secure Mode)

```
┌─────────────────┐
│                 │
│ App Launch      │
│ (Secure Mode)   │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Initialize      │
│ Security        │
│                 │
└────────┬────────┘
         │
         ▼
┌───────────────────────────────────────────────────────────────────┐
│                                                                   │
│                     Security Checks                               │
│                                                                   │
└────────┬────────────────────┬────────────────────┬────────────────┘
         │                    │                    │
         ▼                    ▼                    ▼
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│                 │  │                 │  │                 │
│ Root/Jailbreak  │  │ Emulator        │  │ App Tampering   │
│ Detection       │  │ Detection       │  │ Detection       │
│                 │  │                 │  │                 │
└────────┬────────┘  └────────┬────────┘  └────────┬────────┘
         │                    │                    │
         └────────────────────┼────────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │                 │
                    │ Secure Config   │
                    │ Loading         │
                    │                 │
                    └────────┬────────┘
                             │
                             ▼
                    ┌─────────────────┐
                    │                 │
                    │ Deobfuscate     │
                    │ Config          │
                    │                 │
                    └────────┬────────┘
                             │
                             ▼
                    ┌─────────────────┐
                    │                 │
                    │ Continue App    │
                    │ Initialization  │
                    │                 │
                    └─────────────────┘
```

## 8. Authentication Token Flow

```
┌─────────────────┐
│                 │
│ API Request     │
│ Initiated       │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Check for       │
│ Auth Token      │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Get Firebase    │
│ Current User    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Get ID Token    │
│ with Retry      │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Add Token to    │
│ API Request     │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Send API        │
│ Request         │
│                 │
└─────────────────┘
```

## 9. Notification Flow

```
┌─────────────────┐
│                 │
│ Notification    │
│ Screen          │
│                 │
└────────┬────────┘
         │
         ▼
┌───────────────────────────────────────────┐
│                                           │
│           Notification Tabs               │
│                                           │
└────────────────────┬────────────────────┬─┘
                     │                    │
                     ▼                    ▼
          ┌─────────────────┐   ┌─────────────────┐
          │                 │   │                 │
          │ Notifications   │   │ Follow Requests │
          │                 │   │                 │
          └────────┬────────┘   └────────┬────────┘
                   │                     │
                   ▼                     ▼
          ┌─────────────────┐   ┌─────────────────┐
          │                 │   │                 │
          │ Notification    │   │ Follow Request  │
          │ List            │   │ List            │
          │                 │   │                 │
          └─────────────────┘   └─────────────────┘
```

## 10. Withdrawal Flow

```
┌─────────────────┐
│                 │
│ Withdrawal      │
│ Screen          │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Show Balance    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Enter Amount    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Select Bank     │
│ Account         │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Withdraw Amount │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Process         │
│ Withdrawal      │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Update Balance  │
│                 │
└─────────────────┘
```

## Key Differences Between Regular and Secure Mode

1. **Initialization Process**:
   - Regular mode (`main.dart`): Standard initialization without security checks
   - Secure mode (`main_secure.dart`): Additional security checks and obfuscation

2. **Security Features in Secure Mode**:
   - Root/jailbreak detection
   - Emulator detection
   - App tampering detection
   - Secure configuration loading with obfuscation

3. **Build Process**:
   - Regular build: Standard Flutter build
   - Secure build: Uses `build_secure_app.bat` with obfuscation flags

4. **Configuration Loading**:
   - Regular mode: Loads configuration directly
   - Secure mode: Loads and deobfuscates configuration

The application follows the same routing logic and user flows in both modes, but the secure mode adds additional security layers to protect against reverse engineering and unauthorized access.
