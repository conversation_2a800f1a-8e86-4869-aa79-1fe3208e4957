import 'package:flutter/material.dart';
import 'cricket_refresh_indicator.dart';

abstract class PaginatedListInterface<T> extends StatefulWidget {
  final List<T> list;
  final String emptyTitle;
  final VoidCallback onRefresh;
  final ScrollController scrollController;
  final bool shouldDisposeController;

  const PaginatedListInterface({
    super.key,
    required this.list,
    required this.emptyTitle,
    required this.onRefresh,
    required this.scrollController,
    this.shouldDisposeController = true,
  });

  @override
  PaginatedListInterfaceState<T, PaginatedListInterface<T>> createState();
}

abstract class PaginatedListInterfaceState<T,
    W extends PaginatedListInterface<T>> extends State<W> {
  bool isLoading = false;
  int page = 1;
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<Color?> _colorAnimation;

  TickerProvider get vsyncProvider;

  @override
  void initState() {
    super.initState();
    widget.scrollController.addListener(_onScroll);
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: vsyncProvider,
    )..repeat(reverse: true);

    _opacityAnimation = Tween(begin: 1.0, end: 0.0).animate(_controller);
    _colorAnimation = ColorTween(
            begin: Colors.grey, end: const Color.fromARGB(255, 48, 0, 180))
        .animate(_controller);
  }

  @override
  void dispose() {
    if (widget.shouldDisposeController) widget.scrollController.dispose();
    _controller.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (widget.scrollController.position.pixels ==
            widget.scrollController.position.maxScrollExtent &&
        !isLoading) {
      loadMoreItems();
    }
  }

  Future<void> loadMoreItems() async {
    setState(() {
      isLoading = true;
    });
    page++;

    List<T> additionalList = await getMoreItems();
    setState(() {
      widget.list.addAll(additionalList);
      isLoading = false;
    });
  }

  Future<void> refreshItems() async {
    setState(() {
      page = 1;
      widget.onRefresh();
    });
  }

  Widget buildEmptyList() {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: SizedBox(
        height: MediaQuery.of(context).size.height * 0.7,
        child: Center(
          child: FadeTransition(
            opacity: _opacityAnimation,
            child: AnimatedBuilder(
              animation: _colorAnimation,
              builder: (context, child) {
                return Text(
                  widget.emptyTitle,
                  style: TextStyle(fontSize: 18, color: _colorAnimation.value),
                  textAlign: TextAlign.center,
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Future<List<T>> getMoreItems();

  Widget buildListItem(BuildContext context, int index);

  @override
  Widget build(BuildContext context) {
    return CricketRefreshIndicator(
      onRefresh: refreshItems,
      displacement: 60.0,
      child: widget.list.isEmpty
          ? buildEmptyList()
          : ListView.builder(
              physics: const AlwaysScrollableScrollPhysics(),
              controller: widget.scrollController,
              itemCount: widget.list.length + 1,
              itemBuilder: (context, index) {
                if (index == widget.list.length) {
                  return isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : const SizedBox.shrink();
                }
                return buildListItem(context, index);
              },
            ),
    );
  }
}
