import 'package:flutter/material.dart';

class AppColors extends ThemeExtension<AppColors> {
  final Color cardBackground;
  final Color cardText;
  final Color buttonBackground;
  final Color buttonText;

  const AppColors({
    required this.cardBackground,
    required this.cardText,
    required this.buttonBackground,
    required this.buttonText,
  });

  @override
  AppColors copyWith({
    Color? cardBackground,
    Color? cardText,
    Color? buttonBackground,
    Color? buttonText,
  }) {
    return AppColors(
      cardBackground: cardBackground ?? this.cardBackground,
      cardText: cardText ?? this.cardText,
      buttonBackground: buttonBackground ?? this.buttonBackground,
      buttonText: buttonText ?? this.buttonText,
    );
  }

  @override
  AppColors lerp(ThemeExtension<AppColors>? other, double t) {
    if (other is! AppColors) return this;
    return AppColors(
      cardBackground: Color.lerp(cardBackground, other.cardBackground, t)!,
      cardText: Color.lerp(cardText, other.cardText, t)!,
      buttonBackground:
          Color.lerp(buttonBackground, other.buttonBackground, t)!,
      buttonText: Color.lerp(buttonText, other.buttonText, t)!,
    );
  }

  static AppColors of(BuildContext context) {
    return Theme.of(context).extension<AppColors>()!;
  }
}
