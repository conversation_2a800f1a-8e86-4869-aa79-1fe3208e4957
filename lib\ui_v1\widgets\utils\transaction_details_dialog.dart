import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter/services.dart';

import '../../themes/app_colors.dart';

class TransactionDetailsDialog extends StatelessWidget {
  final String transactionId;
  final int timeStamp;
  final bool success;
  final num amount;
  final String paymentType;

  const TransactionDetailsDialog({
    Key? key,
    required this.transactionId,
    required this.timeStamp,
    required this.success,
    required this.amount,
    required this.paymentType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String formattedAmount =
        (amount is int) ? '₹${(amount as int) / 100}' : '₹$amount';
    return AlertDialog(
      backgroundColor: AppColors.of(context).cardBackground,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      title: const Text(
        'Transaction Details',
        style: TextStyle(
          fontSize: 18.0,
          fontWeight: FontWeight.bold,
          color: Color.fromARGB(255, 48, 0, 180),
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Txn. ID: $transactionId',
                    style: const TextStyle(
                      fontSize: 14.0,
                      fontWeight: FontWeight.w500,
                      // color: Colors.black87,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.copy,
                      color: Color.fromARGB(255, 48, 0, 180)),
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: transactionId));
                    Fluttertoast.showToast(
                        msg: 'Transaction ID copied to clipboard!');
                  },
                ),
              ],
            ),
            _buildDetailRow(
                'Time: ',
                DateTime.fromMillisecondsSinceEpoch(timeStamp)
                    .toString()
                    .substring(0, 16)),
            _buildDetailRow('Status: ', success ? 'Success' : 'Failed'),
            _buildDetailRow('Amount: ', formattedAmount),
            _buildDetailRow('Payment Type: ', paymentType),
            Align(
              alignment: Alignment.bottomRight,
              child: TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text(
                  'Close',
                  style: TextStyle(color: Color.fromARGB(255, 48, 0, 180)),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Text(
            '$label ',
            style: const TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.w500,
              // color: Colors.black54,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14.0,
                // color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
