import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationHelper {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static Future<void> initialize() async {
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/launcher_icon');
    const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
      requestSoundPermission: true,
      requestBadgePermission: true,
      requestAlertPermission: true,
    );
    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    await _notificationsPlugin.initialize(settings);

    FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;

    await firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
    // await firebaseMessaging.subscribeToTopic('global');  disabled for ios
    FirebaseMessaging.onMessage.listen((message) {
      _showNotification(message);
    });
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    await Firebase.initializeApp();
    _showNotification(message);
  }

  static void _showNotification(RemoteMessage message) {
    final notification = message.notification;
    if (notification != null) {
      // _storeNotification(notification.title, notification.body);

      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        'default_channel',
        'Default',
        importance: Importance.max,
        priority: Priority.high,
      );

      const NotificationDetails details = NotificationDetails(
        android: androidDetails,
      );

      _notificationsPlugin.show(
        message.hashCode,
        notification.title,
        notification.body,
        details,
      );
    }
  }

  // static Future<void> _storeNotification(String? title, String? body) async {
  //   if (title == null || body == null) return;

  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   List<String> notifications = prefs.getStringList('notifications') ?? [];

  //   Map<String, dynamic> newNotification = {
  //     'title': title,
  //     'body': body,
  //     'timestamp': DateTime.now().millisecondsSinceEpoch
  //   };

  //   notifications.add(jsonEncode(newNotification));

  //   await prefs.setStringList('notifications', notifications);

  //   _cleanOldNotifications();
  // }

  // static Future<void> _cleanOldNotifications() async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   List<String> notifications = prefs.getStringList('notifications') ?? [];

  //   int sevenDaysAgo =
  //       DateTime.now().subtract(const Duration(days: 7)).millisecondsSinceEpoch;

  //   List<String> filteredNotifications =
  //       notifications.where((notificationJson) {
  //     Map<String, dynamic> notification = jsonDecode(notificationJson);
  //     return notification['timestamp'] > sevenDaysAgo;
  //   }).toList();

  //   await prefs.setStringList('notifications', filteredNotifications);
  // }
}
