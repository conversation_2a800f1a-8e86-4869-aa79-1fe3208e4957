import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// A utility class for managing cached data
class CacheManager {
  static const String _cachePrefix = 'cache_';
  static const Duration _defaultCacheDuration = Duration(minutes: 15);
  
  /// Saves data to cache with an expiration time
  static Future<bool> saveToCache({
    required String key, 
    required dynamic data,
    Duration? cacheDuration,
  }) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String cacheKey = _cachePrefix + key;
      final String jsonData = jsonEncode(data);
      
      // Calculate expiration time
      final int expirationTime = DateTime.now()
          .add(cacheDuration ?? _defaultCacheDuration)
          .millisecondsSinceEpoch;
      
      // Save data and expiration time
      await prefs.setString(cacheKey, jsonData);
      await prefs.setInt('${cacheKey}_expiration', expirationTime);
      
      return true;
    } catch (e) {
      print('Error saving to cache: $e');
      return false;
    }
  }
  
  /// Retrieves data from cache if it exists and is not expired
  static Future<T?> getFromCache<T>({
    required String key,
    required T Function(Map<String, dynamic> json) fromJson,
  }) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String cacheKey = _cachePrefix + key;
      
      // Check if data exists
      if (!prefs.containsKey(cacheKey)) {
        return null;
      }
      
      // Check if data is expired
      final int? expirationTime = prefs.getInt('${cacheKey}_expiration');
      if (expirationTime == null || 
          expirationTime < DateTime.now().millisecondsSinceEpoch) {
        // Data is expired, remove it
        await prefs.remove(cacheKey);
        await prefs.remove('${cacheKey}_expiration');
        return null;
      }
      
      // Data exists and is not expired
      final String? jsonData = prefs.getString(cacheKey);
      if (jsonData == null) return null;
      
      final Map<String, dynamic> data = jsonDecode(jsonData);
      return fromJson(data);
    } catch (e) {
      print('Error retrieving from cache: $e');
      return null;
    }
  }
  
  /// Retrieves a list of items from cache
  static Future<List<T>?> getListFromCache<T>({
    required String key,
    required T Function(Map<String, dynamic> json) fromJson,
  }) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String cacheKey = _cachePrefix + key;
      
      // Check if data exists
      if (!prefs.containsKey(cacheKey)) {
        return null;
      }
      
      // Check if data is expired
      final int? expirationTime = prefs.getInt('${cacheKey}_expiration');
      if (expirationTime == null || 
          expirationTime < DateTime.now().millisecondsSinceEpoch) {
        // Data is expired, remove it
        await prefs.remove(cacheKey);
        await prefs.remove('${cacheKey}_expiration');
        return null;
      }
      
      // Data exists and is not expired
      final String? jsonData = prefs.getString(cacheKey);
      if (jsonData == null) return null;
      
      final List<dynamic> dataList = jsonDecode(jsonData);
      return dataList.map((item) => fromJson(item)).toList();
    } catch (e) {
      print('Error retrieving list from cache: $e');
      return null;
    }
  }
  
  /// Clears all cached data
  static Future<bool> clearCache() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final Set<String> keys = prefs.getKeys();
      
      for (String key in keys) {
        if (key.startsWith(_cachePrefix)) {
          await prefs.remove(key);
        }
      }
      
      return true;
    } catch (e) {
      print('Error clearing cache: $e');
      return false;
    }
  }
  
  /// Removes a specific item from cache
  static Future<bool> removeFromCache(String key) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String cacheKey = _cachePrefix + key;
      
      await prefs.remove(cacheKey);
      await prefs.remove('${cacheKey}_expiration');
      
      return true;
    } catch (e) {
      print('Error removing from cache: $e');
      return false;
    }
  }
  
  /// Checks if a cache key exists and is not expired
  static Future<bool> cacheExists(String key) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String cacheKey = _cachePrefix + key;
      
      // Check if data exists
      if (!prefs.containsKey(cacheKey)) {
        return false;
      }
      
      // Check if data is expired
      final int? expirationTime = prefs.getInt('${cacheKey}_expiration');
      if (expirationTime == null || 
          expirationTime < DateTime.now().millisecondsSinceEpoch) {
        return false;
      }
      
      return true;
    } catch (e) {
      print('Error checking cache existence: $e');
      return false;
    }
  }
}
