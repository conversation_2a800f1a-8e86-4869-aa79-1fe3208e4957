import 'dart:convert';

abstract class BaseTransaction {
  final String userId;
  final int transactionTimestamp;
  final String transactionType;
  final int transactionAmount;

  BaseTransaction({
    required this.userId,
    required int timeStamp,
    required String paymentType,
    required this.transactionAmount,
  })  : transactionTimestamp = timeStamp,
        transactionType = paymentType;

  int get timeStamp => transactionTimestamp;
  String get paymentType => transactionType;
  int get amount => transactionAmount;

  Map<String, dynamic> toJson();

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}
