import 'package:internet_connection_checker/internet_connection_checker.dart';

class NetworkInfo {
  final InternetConnectionChecker connectionChecker;

  NetworkInfo(this.connectionChecker);

  Future<bool> get isConnected => connectionChecker.hasConnection;

  Stream<InternetConnectionStatus> get connectionStatusStream =>
      connectionChecker.onStatusChange;

  Future<bool> isConnectedWithTimeout({Duration? timeout}) async {
    try {
      return await connectionChecker
          .hasConnection
          .timeout(timeout ?? const Duration(seconds: 5));
    } catch (_) {
      return false;
    }
  }
}

class NetworkInfoImpl {
  static final NetworkInfo _instance = NetworkInfo(InternetConnectionChecker());

  static NetworkInfo get instance => _instance;
}
