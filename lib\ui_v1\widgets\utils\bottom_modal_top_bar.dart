import 'package:flutter/material.dart';

class BottomModalTopBar extends StatelessWidget {
  final String addBalanceText;
  final String currentBalanceText;

  const BottomModalTopBar({super.key, 
    required this.addBalanceText,
    required this.currentBalanceText,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            addBalanceText,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Text(
            currentBalanceText,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
