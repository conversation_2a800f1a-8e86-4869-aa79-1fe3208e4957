import 'package:flutter/material.dart';
import '../../backend/model/player.dart';
import '../../backend/model/contest.dart';
import '../Screens/contests/joined_contests.dart';
import '../Screens/contests/show_contest.dart';
import '../screens/contests/show_contests.dart';
import '../screens/profile_screen.dart';
import '../utils/constants.dart';
import '../screens/info_and_settings.dart';
import '../screens/transactions.dart';
import '../screens/rank_screen.dart';
import '../screens/success_screen.dart';
import '../screens/login/phone_sign_in_screen.dart';
import '../../backend/model/match.dart';

import '../screens/main_screen.dart';
import '../screens/notifications.dart';
import '../screens/payment_screen.dart';
import '../screens/wallet.dart';
import '../screens/withdrawals.dart';
import '../screens/login/google_sign_in_screen.dart';
import '../widgets/utils/contest_app_bar.dart';

class Routes {
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case RoutePaths.main:
        return MaterialPageRoute(builder: (_) => const MainScreen());
      case RoutePaths.googleSignInScreen:
        return MaterialPageRoute(builder: (_) => const GoogleSignInScreen());
      case RoutePaths.phoneSignInScreen:
        return MaterialPageRoute(builder: (_) => const PhoneSignInScreen());
      case RoutePaths.showContests:
        return _buildShowContestsRoute(settings.arguments as Set<Object?>?);
      case RoutePaths.showContest:
        return _buildShowContestRoute(settings.arguments as Set<Object?>?);
      case RoutePaths.wallet:
        return MaterialPageRoute(builder: (_) => const Wallet());
      case RoutePaths.profileScreen:
        return _buildProfileScreen(
            settings.arguments as Map<Object?, Object?>?);
      case RoutePaths.successScreen:
        return _buildSuccessScreen(settings.arguments as Set<Object?>?);
      case RoutePaths.joinedContests:
        return _buildJoinedContest(settings.arguments as Set<Object?>?);
      case RoutePaths.rankScreen:
        return _buildRankScreen(settings.arguments as Set<Object?>?);
      case RoutePaths.paymentScreen:
        return MaterialPageRoute(builder: (_) => const PaymentScreen());
      case RoutePaths.allTransactions:
        return MaterialPageRoute(builder: (_) => const Transactions());
      case RoutePaths.notifications:
        return MaterialPageRoute(builder: (_) => const Notifications());
      case RoutePaths.withdrawals:
        return MaterialPageRoute(builder: (_) => const Withdrawals());
      case RoutePaths.infoAndSettings:
        return _buildSettingsScreen(settings.arguments as Set<Object?>?);
      default:
        return _errorRoute();
    }
  }

  static MaterialPageRoute _buildShowContestsRoute(Set<Object?>? arguments) {
    if (arguments != null) {
      final Match? match =
          arguments.isNotEmpty ? arguments.first as Match? : null;
      final ContestAppBar? contestAppBar = arguments.length > 1
          ? arguments.elementAt(1) as ContestAppBar?
          : null;
      return MaterialPageRoute(
        builder: (_) => ShowContests(match, contestAppBar),
      );
    } else {
      print('Unexpected arguments type for showContests');
      return _errorRoute();
    }
  }

  static MaterialPageRoute _buildShowContestRoute(Set<Object?>? arguments) {
    if (arguments != null) {
      final Contest? contest =
          arguments.isNotEmpty ? arguments.first as Contest? : null;
      final Match? match =
          arguments.length > 1 ? arguments.elementAt(1) as Match? : null;
      final ContestAppBar? contestAppBar = arguments.length > 2
          ? arguments.elementAt(2) as ContestAppBar?
          : null;
      final List<Player>? top3List =
          arguments.length > 3 ? arguments.elementAt(3) as List<Player>? : null;
      return MaterialPageRoute(
        builder: (_) => ShowContest(
          contest: contest,
          match: match,
          contestAppBar: contestAppBar,
          top3List: top3List,
        ),
      );
    } else {
      print('Unexpected arguments type for showContest');
      return _errorRoute();
    }
  }

  static MaterialPageRoute _buildSuccessScreen(Set<Object?>? arguments) {
    if (arguments != null) {
      final List<IconData> iconList = arguments.elementAt(0) as List<IconData>;
      final List<Player> top3List = arguments.elementAt(1) as List<Player>;
      final Match match = arguments.elementAt(2) as Match;
      final ContestAppBar contestAppBar =
          arguments.elementAt(3) as ContestAppBar;
      final bool joinedContest = arguments.elementAt(4) as bool;
      return MaterialPageRoute(
        builder: (_) => SuccessScreen(
            top3List, iconList, match, contestAppBar, joinedContest),
      );
    } else {
      print('Unexpected arguments type for showContest');
      return _errorRoute();
    }
  }

  static MaterialPageRoute _buildJoinedContest(Set<Object?>? arguments) {
    if (arguments != null) {
      final Match upcomingMatch = arguments.elementAt(0) as Match;
      final Map<String, dynamic> map =
          arguments.elementAt(1) as Map<String, dynamic>;
      return MaterialPageRoute(
          builder: (_) => JoinedContests(upcomingMatch, map));
    } else {
      print('Unexpected arguments type for showContest');
      return _errorRoute();
    }
  }

  static MaterialPageRoute _buildRankScreen(Set<Object?>? arguments) {
    if (arguments != null) {
      final String upcomingMatchId = arguments.elementAt(0).toString();
      final String contestEntry = arguments.elementAt(1).toString();
      return MaterialPageRoute(
          builder: (_) => RankScreen(
                upcomingMatchId: upcomingMatchId,
                contestEntry: contestEntry,
              ));
    } else {
      print('Unexpected arguments type for RankScreen');
      return _errorRoute();
    }
  }

  static MaterialPageRoute _buildProfileScreen(
      Map<Object?, Object?>? arguments) {
    if (arguments != null) {
      final String name = arguments['name'].toString();
      final String photoUrl = arguments['photoUrl'].toString();
      final bool isCurrentUser = arguments['isCurrentUser'] as bool;
      final String followers = arguments['followers'].toString();
      final String following = arguments['following'].toString();
      final String balance = arguments['balance'].toString();
      final String totalWinnings = arguments['totalWinnings'].toString();
      final String contestsJoined = arguments['contestsJoined'].toString();

      return MaterialPageRoute(
          builder: (_) => ProfileScreen(
                name: name,
                photoUrl: photoUrl,
                isCurrentUser: isCurrentUser,
                followers: followers,
                following: following,
                balance: balance,
                totalWinnings: totalWinnings,
                contestsJoined: contestsJoined,
              ));
    } else {
      print('Unexpected arguments type for RankScreen');
      return _errorRoute();
    }
  }

  static MaterialPageRoute _buildSettingsScreen(Set<Object?>? arguments) {
    if (arguments != null) {
      final String name = arguments.elementAt(0).toString();
      final String uid = arguments.elementAt(1).toString();
      final String email = arguments.elementAt(2).toString();
      final bool existingUser = arguments.elementAt(3) as bool;
      final String? photoUrl =
          arguments.length == 5 ? arguments.elementAt(4).toString() : null;
      return MaterialPageRoute(
          builder: (_) => InfoAndSettings(
                userName: name,
                userId: uid,
                userEmail: email,
                existingUser: existingUser,
                photoUrl: photoUrl,
              ));
    } else {
      print('Unexpected arguments type for RankScreen');
      return _errorRoute();
    }
  }

  static MaterialPageRoute _errorRoute() {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: const Center(child: Text('Something went wrong!')),
      ),
    );
  }
}
// flutter config --jdk-dir="path/to/jdk"