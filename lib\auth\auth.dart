import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../core/utils/logger.dart';
import 'auth_details.dart';
import '../core/error/exceptions.dart';
import '../core/utils/retry_helper.dart';

class Auth {
  static final FirebaseAuth firebaseAuth = FirebaseAuth.instance;
  static final GoogleSignIn googleSignIn = GoogleSignIn(scopes: ['email']);

  static Future<User> login() async {
    try {
      Logger.info('Starting Google sign-in process');
      final googleAccount = await googleSignIn.signIn();
      if (googleAccount == null) {
        throw AuthException("Sign-in aborted by user", code: "SIGN_IN_ABORTED");
      }
      Logger.info('Google account selected: ${googleAccount.email}');

      try {
        Logger.info('Retrieving Google authentication tokens');
        final googleAuth = await googleAccount.authentication;
        if (googleAuth.accessToken == null || googleAuth.idToken == null) {
          throw AuthException(
            'Failed to get authentication tokens',
            code: 'MISSING_AUTH_TOKENS',
          );
        }
        Logger.info('Successfully retrieved Google authentication tokens');

        try {
          final credential = GoogleAuthProvider.credential(
            accessToken: googleAuth.accessToken,
            idToken: googleAuth.idToken,
          );

          Logger.info('Signing in to Firebase with Google credential');
          final userCredential = await firebaseAuth.signInWithCredential(credential);
          final user = userCredential.user;

          if (user == null || user.isAnonymous) {
            throw AuthException(
              'User is null or anonymous',
              code: 'USER_NULL_OR_ANONYMOUS',
            );
          }
          Logger.info('Firebase user retrieved: ${user.uid}');

          final firebaseUser = firebaseAuth.currentUser;
          if (firebaseUser?.uid != user.uid) {
            throw AuthException(
              'Logged in user does not match current user',
              code: 'USER_MISMATCH',
            );
          }

          Logger.info('Successfully authenticated user: ${user.uid}');

          return user;
        } catch (e) {
          if (e is TypeError) {
            Logger.error('Type error during Firebase credential processing', error: e, stackTrace: StackTrace.current);
            throw AuthException(
              'Login failed due to type mismatch in Firebase credential. Please try again.',
              code: 'FIREBASE_TYPE_ERROR',
              details: e,
            );
          }
          rethrow;
        }
      } catch (e) {
        if (e is TypeError) {
          Logger.error('Type error during Google authentication', error: e, stackTrace: StackTrace.current);
          throw AuthException(
            'Login failed due to type mismatch in Google authentication. Please try again.',
            code: 'GOOGLE_AUTH_TYPE_ERROR',
            details: e,
          );
        }
        rethrow;
      }
    } on FirebaseAuthException catch (e) {
      Logger.error('Firebase auth error', error: e);
      throw AuthException(
        e.message ?? 'Authentication failed',
        code: e.code,
        details: e,
      );
    } on AuthException {
      rethrow;
    } on TypeError catch (e) {
      Logger.error('Type error during login process', error: e, stackTrace: StackTrace.current);
      throw AuthException(
        'Login failed due to type mismatch. Please try again.',
        code: 'TYPE_ERROR',
        details: e,
      );
    } catch (e) {
      Logger.error('Login failed', error: e, stackTrace: StackTrace.current);
      throw AuthException(
        'Login failed: ${e.toString()}',
        details: e,
      );
    }
  }

  static Future<void> signout() async {
    try {
      Logger.info('Signing out user');
      await firebaseAuth.signOut();
      await googleSignIn.signOut();
      AuthDetails.setIfLoggedIn(false);
      Logger.info('User signed out successfully');
    } catch (e) {
      Logger.error('Sign out failed', error: e);
      throw AuthException('Failed to sign out: ${e.toString()}');
    }
  }

  static Future<String?> getToken() async {
    return RetryHelper.retry(
      operation: () async {
        try {
          final user = firebaseAuth.currentUser;
          if (user != null) {
            final idToken = await user.getIdToken();
            return idToken;
          }
          return null;
        } on FirebaseAuthException catch (e) {
          Logger.error('Firebase token error', error: e);
          throw AuthException(
            'Failed to get authentication token',
            code: e.code,
            details: e,
          );
        } catch (e) {
          Logger.error('Error getting token', error: e);
          throw AuthException('Failed to get authentication token');
        }
      },
      maxRetries: 2,
    );
  }
}
