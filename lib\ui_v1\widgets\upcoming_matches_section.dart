import 'package:flutter/material.dart';
import '../../backend/model/match.dart';
import '../utils/constants.dart';
import '../utils/context.dart';
import 'cards/upcoming_match_card.dart';
import 'utils/contest_app_bar.dart';  
import 'utils/timer/match_countdown_timer.dart';

class UpcomingMatchesSection extends StatefulWidget {
  const UpcomingMatchesSection({super.key});

  @override
  UpcomingMatchesSectionState createState() => UpcomingMatchesSectionState();
}

class UpcomingMatchesSectionState extends State<UpcomingMatchesSection> {
  Future<List<Match>>? _upcomingMatchesFuture;
  late dynamic matchCountdownTimer;

  @override
  void initState() {
    super.initState();
    _upcomingMatchesFuture = getUpcomingMatches();
  }

  Future<void> refreshItems() async {
    setState(() {
      Future.delayed(const Duration(seconds: 2));
      _upcomingMatchesFuture = getUpcomingMatches();
    });
  }

  Future<List<Match>> getUpcomingMatches() async {
    // Use the improved Context class method to get matches
    return await Context.getUpcomingMatches();
  }

  @override
  void dispose() {
    matchCountdownTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final MediaQueryData mediaQuery = MediaQuery.of(context);
    final double screenHeight = mediaQuery.size.height;
    final double screenWidth = mediaQuery.size.width;

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Padding(
            padding: EdgeInsets.only(
                top: screenHeight * 0.02, bottom: screenHeight * 0.01),
            child: Text(
              "Upcoming Matches",
              style: TextStyle(
                fontSize: screenWidth * 0.055,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: FutureBuilder<List<Match>>(
                future: _upcomingMatchesFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return const Center(child: Text('Error loading matches'));
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return const Center(child: Text('No upcoming matches'));
                  }
                  List<Match> matches = snapshot.data!;
                  return ListView.builder(
                    itemCount: matches.length,
                    itemBuilder: (context, index) {
                      var match = matches[index];
                      matchCountdownTimer = MatchCountdownTimer(
                          targetDateTime: match.startDate,
                          onTick: (remainingTime) {},
                          onEnd: () {
                            refreshItems();
                          });
                      return Padding(
                        padding:
                            EdgeInsets.symmetric(vertical: screenHeight * 0.01),
                        child: UpcomingMatchCard(
                          match: match,
                          onTap: () {
                            ContestAppBar contestAppBar = ContestAppBar(match);
                            Navigator.pushNamed(
                              context,
                              RoutePaths.showContests,
                              arguments: {match, contestAppBar},
                            );
                          },
                        ),
                      );
                    },
                  );
                }),
          ),
        ],
      ),
    );
  }
}
