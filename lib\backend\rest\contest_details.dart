import '../model/base_transaction.dart';
import '../model/game_transaction.dart';
import '../model/bank_transaction.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import 'api_service.dart';

class ContestDetails {
  static const String apiUrl =
      'https://62zf08ch4l.execute-api.ap-south-1.amazonaws.com/contest-details';

  static Future<http.Response> saveUserContestDetails(
      String uid,
      String matchId,
      String contestId,
      String top1,
      String top2,
      String top3) async {
    final body = {
      'userId': uid,
      'matchId': matchId,
      'contestId': contestId,
      'top1': top1,
      'top2': top2,
      'top3': top3,
    };

    return await ApiService.postRequest(apiUrl, 'saveUserContestDetails', body);
  }

  static Future<List<String>> getUserMatchList(String userId) async {
    final body = {'userId': userId};
    final response =
        await ApiService.postRequest(apiUrl, 'getAllMatchesByUserId', body);
    return List<String>.from(json.decode(response.body));
  }

  static Future<int> getUserContestCount(String userId, String matchId) async {
    final body = {'userId': userId, 'matchId': matchId};
    final response =
        await ApiService.postRequest(apiUrl, 'getUserContestCount', body);
    return int.parse(response.body);
  }

  static Future<int> getTotalUserContestCount(String userId) async {
    final body = {'userId': userId};
    final response =
        await ApiService.postRequest(apiUrl, 'getTotalUserContestCount', body);
    return int.parse(response.body);
  }

  static Future<List<String>> getUserContestList(
      String userId, String matchId) async {
    final body = {'userId': userId, 'matchId': matchId};
    final response =
        await ApiService.postRequest(apiUrl, 'getUserContestList', body);
    return List<String>.from(json.decode(response.body));
  }

  static Future<Map<String, dynamic>> getNumberOfContestsJoinedInMatch(
      String userId) async {
    final body = {'userId': userId};
    final response = await ApiService.postRequest(
        apiUrl, 'getNumberOfContestsJoinedInMatch', body);
    return json.decode(response.body);
  }

  static Future<List<Map<String, dynamic>>> getTop3List(
      String userId, String matchId) async {
    final body = {'userId': userId, 'matchId': matchId};
    final response = await ApiService.postRequest(apiUrl, 'getTop3List', body);
    return List<Map<String, dynamic>>.from(json.decode(response.body));
  }

  static Future<http.Response> updateUserContestDetails(
      String uid,
      String matchId,
      String contestId,
      String top1,
      String top2,
      String top3) async {
    final body = {
      'userId': uid,
      'matchId': matchId,
      'contestId': contestId,
      'top1': top1,
      'top2': top2,
      'top3': top3,
    };

    return await ApiService.postRequest(
        apiUrl, 'updateUserContestDetails', body);
  }

  static Future<Map<String, String>> getMatchContestDetails(
      String matchId) async {
    final body = {'matchId': matchId};
    final response =
        await ApiService.postRequest(apiUrl, 'getMatchContestDetails', body);

    if (response.body.toString().isEmpty || response.body.contains("OK")) {
      return {"19": "0", "49": "0", "99": "0"};
    } else {
      return Map<String, String>.from(json
          .decode(response.body)
          .map((key, value) => MapEntry(key.toString(), value.toString())));
    }
  }

  static Future<void> saveMatchContestDetails(
      String userId, String matchId, String entry) async {
    final checkBody = {'matchId': matchId};
    final checkResponse = await ApiService.postRequest(
        apiUrl, 'getMatchContestDetails', checkBody);

    if (checkResponse.body.toString().isEmpty ||
        checkResponse.body.contains("OK")) {
      final saveBody = {'userId': userId, 'matchId': matchId, 'entry': entry};
      await ApiService.postRequest(apiUrl, 'saveMatchContestDetails', saveBody);
    } else {
      final updateBody = {'userId': userId, 'matchId': matchId, 'entry': entry};
      await ApiService.postRequest(
          apiUrl, 'updateMatchContestDetails', updateBody);
    }
  }

  static Future<List<BaseTransaction>> getAllTransactions(
      String userId, int page) async {
    final response = await ApiService.postRequest(
        apiUrl, 'getAllTransactions', {'userId': userId, 'page': page});
    return (jsonDecode(response.body) as List<dynamic>)
        .map<BaseTransaction>((json) => json.containsKey('paymentDetails')
            ? BankTransaction.fromJson(json)
            : GameTransaction.fromJson(json))
        .toList();
  }
}
