import 'package:flutter/material.dart';

/// A memory-efficient list view that only builds visible items
class OptimizedListView<T> extends StatefulWidget {
  /// The list of items to display
  final List<T> items;
  
  /// A builder function that creates a widget for each item
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  
  /// A builder function that creates a widget when the list is empty
  final Widget Function(BuildContext context)? emptyBuilder;
  
  /// A builder function that creates a loading widget
  final Widget Function(BuildContext context)? loadingBuilder;
  
  /// Whether the list is currently loading
  final bool isLoading;
  
  /// The scroll controller for the list
  final ScrollController? scrollController;
  
  /// The scroll physics for the list
  final ScrollPhysics? physics;
  
  /// The padding for the list
  final EdgeInsetsGeometry? padding;
  
  /// Whether to shrink wrap the list
  final bool shrinkWrap;
  
  /// The function to call when the list is refreshed
  final Future<void> Function()? onRefresh;
  
  /// The function to call when the list reaches the end
  final Future<void> Function()? onLoadMore;
  
  /// The threshold for triggering onLoadMore (0.0 to 1.0)
  final double loadMoreThreshold;
  
  /// The separator builder for the list
  final Widget Function(BuildContext, int)? separatorBuilder;
  
  /// Creates an OptimizedListView
  const OptimizedListView({
    Key? key,
    required this.items,
    required this.itemBuilder,
    this.emptyBuilder,
    this.loadingBuilder,
    this.isLoading = false,
    this.scrollController,
    this.physics,
    this.padding,
    this.shrinkWrap = false,
    this.onRefresh,
    this.onLoadMore,
    this.loadMoreThreshold = 0.8,
    this.separatorBuilder,
  }) : super(key: key);

  @override
  State<OptimizedListView<T>> createState() => _OptimizedListViewState<T>();
}

class _OptimizedListViewState<T> extends State<OptimizedListView<T>> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    
    if (widget.onLoadMore != null) {
      _scrollController.addListener(_scrollListener);
    }
  }

  @override
  void dispose() {
    if (widget.onLoadMore != null) {
      _scrollController.removeListener(_scrollListener);
    }
    
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    
    super.dispose();
  }

  void _scrollListener() {
    if (_isLoadingMore || widget.onLoadMore == null) return;
    
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;
    final threshold = maxScroll * widget.loadMoreThreshold;
    
    if (currentScroll >= threshold) {
      setState(() {
        _isLoadingMore = true;
      });
      
      widget.onLoadMore!().then((_) {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty && !widget.isLoading) {
      return widget.emptyBuilder?.call(context) ?? 
          const Center(child: Text('No items'));
    }
    
    Widget listView = widget.separatorBuilder != null
        ? ListView.separated(
            controller: _scrollController,
            physics: widget.physics,
            padding: widget.padding,
            shrinkWrap: widget.shrinkWrap,
            itemCount: widget.items.length + (_isLoadingMore ? 1 : 0),
            separatorBuilder: widget.separatorBuilder!,
            itemBuilder: (context, index) {
              if (index == widget.items.length) {
                return widget.loadingBuilder?.call(context) ?? 
                    const Center(child: CircularProgressIndicator());
              }
              return widget.itemBuilder(context, widget.items[index], index);
            },
          )
        : ListView.builder(
            controller: _scrollController,
            physics: widget.physics,
            padding: widget.padding,
            shrinkWrap: widget.shrinkWrap,
            itemCount: widget.items.length + (_isLoadingMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == widget.items.length) {
                return widget.loadingBuilder?.call(context) ?? 
                    const Center(child: CircularProgressIndicator());
              }
              return widget.itemBuilder(context, widget.items[index], index);
            },
          );
    
    if (widget.onRefresh != null) {
      return RefreshIndicator(
        onRefresh: widget.onRefresh!,
        child: listView,
      );
    }
    
    return listView;
  }
}
