import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../Auth/auth_details.dart';
import '../../backend/rest/user_details.dart';
import '../utils/user_context.dart';
import '../widgets/payment_service.dart';
import '../widgets/utils/add_amount_button.dart';
import '../widgets/utils/amount_text_box.dart';

class PaymentScreen extends StatefulWidget {
  const PaymentScreen({super.key});

  @override
  _PaymentScreenState createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final TextEditingController _textEditingController = TextEditingController();
  bool _isLoading = false;
  final PaymentService _paymentService = PaymentService();
  double amount = 0.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("PhonePe Payment"),
      ),
      body: Stack(
        children: [
          Column(
            children: [
              AmountTextBox(
                controller: _textEditingController,
                labelText: 'Enter amount to add',
              ),
              AddAmountButton(
                textEditingController: _textEditingController,
                onPressed: () {
                  setState(() {
                    _isLoading = false;
                  });
                  _startTransaction();
                },
                onValueSelected: (value) => amount = value,
              ),
            ],
          ),
          if (_isLoading)
            Container(
              color: Colors.grey.withOpacity(0.5),
              child: const Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }

  void _startTransaction() {
    _paymentService.startTransaction(
      amount,
      () async {
        await UserDetails.getUserDetails(UserContext.userId)
            .then((userDetails) {
          AuthDetails.setUserBalance(userDetails);
        });
        UserContext.refreshBalance();
        Fluttertoast.showToast(msg: "Transaction Successful!");
        setState(() {
          _isLoading = false;
        });
      },
      (errorMessage) {
        Fluttertoast.showToast(msg: errorMessage);
        setState(() {
          _isLoading = false;
        });
      },
    );
  }
}
