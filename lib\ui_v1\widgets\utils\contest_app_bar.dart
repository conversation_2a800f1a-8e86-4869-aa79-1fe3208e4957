import 'package:flutter/material.dart';
import '../../../backend/model/match.dart';
import '../../themes/app_colors.dart';
import 'timer/match_countdown_timer.dart';

class ContestAppBar extends StatefulWidget implements PreferredSizeWidget {
  final Match? match;

  const ContestAppBar(this.match, {super.key});

  @override
  _ContestAppBarState createState() => _ContestAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _ContestAppBarState extends State<ContestAppBar> {
  late Duration remainingTime;
  late MatchCountdownTimer timer;
  bool _isTimeInitialized = false;

  @override
  void initState() {
    super.initState();
    remainingTime = Duration.zero;
    timer = MatchCountdownTimer(
        targetDateTime: widget.match!.startDate,
        onTick: (remainingTime) {
          setState(() {
            this.remainingTime = remainingTime;
            _isTimeInitialized =
                true; // Set to true once the time is initialized
          });
        },
        onEnd: () {
          Navigator.pop(context);
        });
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
        backgroundColor: AppColors.of(context).cardBackground,
        title: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "${widget.match?.team1 ?? "Unknown"} vs ${widget.match?.team2 ?? "Unknown"}",
            ),
            _isTimeInitialized
                ? Text(
                    '${remainingTime.inHours}h ${remainingTime.inMinutes.remainder(60)}m ${remainingTime.inSeconds.remainder(60)}s left',
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        ));
  }

  Duration _calculateRemainingTime(Match? match) {
    DateTime currentTime = DateTime.now();
    DateTime parsedStartDate =
        DateTime.fromMillisecondsSinceEpoch(int.parse(match!.startDate));
    return parsedStartDate.difference(currentTime);
  }
}
