import 'package:flutter/material.dart';
import 'app_theme.dart';
import 'app_colors.dart';

class LightTheme extends AppTheme {
  @override
  ThemeData buildThemeData() {
    return ThemeData(
      brightness: Brightness.light,
      cardTheme: getCardTheme(),
      // elevatedButtonTheme: getElevatedButtonTheme().copyWith(
      //   style: ButtonStyle(
      //     backgroundColor: MaterialStateProperty.all(Colors.blue),
      //   ),
      // ),
      scaffoldBackgroundColor: const Color(0xFFF4F4F4),
      extensions: const [
        AppColors(
          cardBackground: Colors.white,
          cardText: Colors.black,
          buttonBackground: Colors.deepPurple,
          buttonText: Colors.white,
        ),
      ],
    );
  }
}
