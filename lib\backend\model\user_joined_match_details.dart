class UserJoinedMatchDetails {
  final String matchId;
  final String team1;
  final String team2;
  final String team1ImagePath;
  final String team2ImagePath;
  final String matchType;
  final String matchFormat;
  final String matchState;
  final String team1Id;
  final String team2Id;
  final String startDate;

  UserJoinedMatchDetails(
    this.matchId,
    this.team1Id,
    this.team2Id,
    this.team1,
    this.team2,
    this.team1ImagePath,
    this.team2ImagePath,
    this.matchType,
    this.matchFormat,
    this.matchState,
    this.startDate,
  );

  String get team1ImageId => team1ImagePath;
  String get team2ImageId => team2ImagePath;
  String get state => matchState;
}