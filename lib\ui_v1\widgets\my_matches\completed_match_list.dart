import 'package:flutter/material.dart';
import '../../../backend/model/match.dart';
import '../../../backend/rest/match_details.dart';
import '../../utils/user_context.dart';
import 'base_match_list.dart';


class CompletedMatchList extends BaseMatchList {
 CompletedMatchList({
    super.key,
    required super.list,
    required super.emptyTitle,
    required super.onRefresh,
  });

  @override
  _CompletedMatchListState createState() => _CompletedMatchListState();
}

class _CompletedMatchListState extends BaseMatchListState
    with SingleTickerProviderStateMixin {
  @override
  TickerProvider get vsyncProvider => this;

  @override
  Future<List<Match>> getMoreItems() async {
    return await MatchDetails.getUserCompletedMatchesByPage(
        UserContext.userId, page);
  }
}
