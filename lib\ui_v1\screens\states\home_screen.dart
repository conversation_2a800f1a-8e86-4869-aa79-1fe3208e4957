import 'package:flutter/material.dart';
import '../../widgets/joined_matches.dart';
import '../../widgets/upcoming_matches_section.dart';
import '../../widgets/utils/cricket_refresh_indicator.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final GlobalKey<JoinedMatchesState> child1Key = GlobalKey();
  final GlobalKey<UpcomingMatchesSectionState> child2Key = GlobalKey();

  @override
  void initState() {
    super.initState();
  }

  Future<void> _refreshItems() async {
    child1Key.currentState?.fetchContests();
    child2Key.currentState?.refreshItems();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;

    return CricketRefreshIndicator(
      onRefresh: _refreshItems,
      displacement: 120.0,
      backgroundColor: Colors.teal.withValues(alpha: 0.15),
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.04, vertical: screenHeight * 0.01),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              JoinedMatches(
                key: child1Key,
              ),
              UpcomingMatchesSection(
                key: child2Key,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
