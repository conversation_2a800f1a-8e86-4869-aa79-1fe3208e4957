class User {
  final String userId;
  final String name;
  final String photoUrl;
  final int rank;

  User({
    required this.userId,
    required this.name,
    required this.photoUrl,
    required this.rank,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userId: json['userId'] as String,
      name: json['name'] as String,
      photoUrl: json['photoUrl'] as String,
      rank: json['rank'] as int,
    );
  }
}
