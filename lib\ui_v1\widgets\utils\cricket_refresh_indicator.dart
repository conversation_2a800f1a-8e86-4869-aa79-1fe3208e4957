import 'package:flutter/material.dart';
import 'elastic_refresh_indicator.dart';

class CricketRefreshIndicator extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double displacement;
  final Color? backgroundColor;
  final Color? color;

  const CricketRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.displacement = 40.0,
    this.backgroundColor,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return ElasticRefreshIndicator(
      onRefresh: onRefresh,
      displacement: displacement,
      backgroundColor: backgroundColor,
      child: child,
    );
  }
}
