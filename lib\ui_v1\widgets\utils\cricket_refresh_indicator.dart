import 'package:flutter/material.dart';
import 'dart:math' as math;

class CricketRefreshIndicator extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double displacement;
  final Color? backgroundColor;
  final Color? color;

  const CricketRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.displacement = 40.0,
    this.backgroundColor,
    this.color,
  });

  @override
  State<CricketRefreshIndicator> createState() => _CricketRefreshIndicatorState();
}

class _CricketRefreshIndicatorState extends State<CricketRefreshIndicator>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  Widget _buildCricketBall() {
    return AnimatedBuilder(
      animation: Listenable.merge([_rotationAnimation, _scaleAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: const RadialGradient(
                  colors: [
                    Color(0xFFDC143C), // Crimson red
                    Color(0xFF8B0000), // Dark red
                  ],
                  stops: [0.3, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(2, 2),
                  ),
                ],
              ),
              child: CustomPaint(
                painter: CricketBallPainter(),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator.adaptive(
      onRefresh: () async {
        _rotationController.repeat();
        _scaleController.forward().then((_) {
          _scaleController.reverse();
        });

        try {
          await widget.onRefresh();
        } finally {
          _rotationController.stop();
          _rotationController.reset();
        }
      },
      displacement: widget.displacement,
      backgroundColor: widget.backgroundColor ?? Colors.transparent,
      color: Colors.transparent,
      strokeWidth: 0, // Hide default indicator
      child: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
          if (notification is ScrollUpdateNotification) {
            // Handle custom pull-down animation here if needed
            if (notification.metrics.pixels < -50) {
              // User has pulled down significantly
              if (!_scaleController.isAnimating) {
                _scaleController.forward().then((_) {
                  _scaleController.reverse();
                });
              }
            }
          }
          return false;
        },
        child: Stack(
          children: [
            widget.child,
            // Custom cricket ball indicator overlay
            Positioned(
              top: widget.displacement - 20,
              left: MediaQuery.of(context).size.width / 2 - 20,
              child: AnimatedBuilder(
                animation: _rotationController,
                builder: (context, child) {
                  return Opacity(
                    opacity: _rotationController.isAnimating ? 1.0 : 0.0,
                    child: _buildCricketBall(),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CricketBallPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw the seam (curved line across the ball)
    final path = Path();

    // Left curve
    path.moveTo(center.dx - radius * 0.8, center.dy - radius * 0.3);
    path.quadraticBezierTo(
      center.dx - radius * 0.3, center.dy - radius * 0.1,
      center.dx, center.dy,
    );

    // Right curve
    path.quadraticBezierTo(
      center.dx + radius * 0.3, center.dy + radius * 0.1,
      center.dx + radius * 0.8, center.dy + radius * 0.3,
    );

    canvas.drawPath(path, paint);

    // Draw stitching marks
    final stitchPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // Left side stitches
    for (int i = 0; i < 6; i++) {
      final t = i / 5.0;
      final x = center.dx - radius * 0.6 + (radius * 0.6 * t);
      final y = center.dy - radius * 0.2 + (radius * 0.4 * t);

      canvas.drawLine(
        Offset(x - 3, y - 1),
        Offset(x + 3, y + 1),
        stitchPaint,
      );
    }

    // Right side stitches
    for (int i = 0; i < 6; i++) {
      final t = i / 5.0;
      final x = center.dx + (radius * 0.6 * t);
      final y = center.dy - radius * 0.1 + (radius * 0.2 * t);

      canvas.drawLine(
        Offset(x - 3, y - 1),
        Offset(x + 3, y + 1),
        stitchPaint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
