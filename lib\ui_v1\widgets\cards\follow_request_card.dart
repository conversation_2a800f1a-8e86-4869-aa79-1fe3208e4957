import 'package:flutter/material.dart';

import '../../themes/app_colors.dart';

class FollowRequestCard extends StatelessWidget {
  final String name;
  final String message;
  final String timeAgo;
  final String imageUrl;
  final VoidCallback onConfirm;
  final VoidCallback onDelete;

  const FollowRequestCard({
    Key? key,
    required this.name,
    required this.message,
    required this.timeAgo,
    required this.imageUrl,
    required this.onConfirm,
    required this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 1,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundImage: NetworkImage(imageUrl),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                          style: TextStyle(
                              color: AppColors.of(context).cardText,
                              fontSize: 16),
                          children: [
                            TextSpan(
                              text: name,
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            TextSpan(text: " $message"),
                          ],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        timeAgo,
                        style:
                            const TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                      Row(
                        children: [
                          ElevatedButton(
                            onPressed: onConfirm,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.deepPurple,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text("Confirm",
                                style: TextStyle(color: Colors.white)),
                          ),
                          const SizedBox(width: 8),
                          OutlinedButton(
                            onPressed: onDelete,
                            style: OutlinedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text("Delete",
                                style: TextStyle(
                                    color: AppColors.of(context).cardText)),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
