import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class ImageHelper {
  static const String playerImageBaseUrl = "https://top3-images.s3.ap-south-1.amazonaws.com/cricket/players/";
  static const String flagImageBaseUrl = "https://top3-images.s3.ap-south-1.amazonaws.com/cricket/flags/";
  
  /// Returns a CachedNetworkImage widget for player images
  static Widget getPlayerImage({
    required String imagePath,
    double? width,
    double? height,
    double? radius,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    final String fullUrl = playerImageBaseUrl + imagePath;
    
    if (radius != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: _getCachedImage(
          imageUrl: fullUrl,
          width: width,
          height: height,
          fit: fit,
          placeholder: placeholder,
          errorWidget: errorWidget,
        ),
      );
    }
    
    return _getCachedImage(
      imageUrl: fullUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: placeholder,
      errorWidget: errorWidget,
    );
  }
  
  /// Returns a CachedNetworkImage widget for flag images
  static Widget getFlagImage({
    required String imagePath,
    double? width,
    double? height,
    double? radius,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    final String fullUrl = flagImageBaseUrl + imagePath;
    
    if (radius != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: _getCachedImage(
          imageUrl: fullUrl,
          width: width,
          height: height,
          fit: fit,
          placeholder: placeholder,
          errorWidget: errorWidget,
        ),
      );
    }
    
    return _getCachedImage(
      imageUrl: fullUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: placeholder,
      errorWidget: errorWidget,
    );
  }
  
  /// Returns a CachedNetworkImage widget for any URL
  static Widget getNetworkImage({
    required String imageUrl,
    double? width,
    double? height,
    double? radius,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    if (radius != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: _getCachedImage(
          imageUrl: imageUrl,
          width: width,
          height: height,
          fit: fit,
          placeholder: placeholder,
          errorWidget: errorWidget,
        ),
      );
    }
    
    return _getCachedImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: placeholder,
      errorWidget: errorWidget,
    );
  }
  
  /// Returns a CircleAvatar with a cached network image
  static Widget getCircleAvatar({
    required String imageUrl,
    required double radius,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      imageBuilder: (context, imageProvider) => CircleAvatar(
        radius: radius,
        backgroundImage: imageProvider,
      ),
      placeholder: (context, url) => placeholder ?? CircleAvatar(
        radius: radius,
        backgroundColor: Colors.grey[300],
        child: SizedBox(
          width: radius,
          height: radius,
          child: const CircularProgressIndicator(
            strokeWidth: 2.0,
          ),
        ),
      ),
      errorWidget: (context, url, error) => errorWidget ?? CircleAvatar(
        radius: radius,
        backgroundColor: Colors.grey[300],
        child: Icon(
          Icons.error,
          size: radius * 0.8,
        ),
      ),
    );
  }
  
  /// Private helper method to create a CachedNetworkImage
  static Widget _getCachedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) => placeholder ?? Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: const Center(
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
          ),
        ),
      ),
      errorWidget: (context, url, error) => errorWidget ?? Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: const Center(
          child: Icon(Icons.error),
        ),
      ),
    );
  }
}
