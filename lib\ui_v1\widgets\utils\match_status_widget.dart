import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

class MatchStatusWidget extends StatefulWidget {
  final String matchState;
  final String startDate;
  final Duration remainingTime;

  const MatchStatusWidget({
    super.key,
    required this.matchState,
    required this.startDate,
    required this.remainingTime,
  });

  @override
  _MatchStatusWidgetState createState() => _MatchStatusWidgetState();
}

class _MatchStatusWidgetState extends State<MatchStatusWidget> {
  @override
  Widget build(BuildContext context) {
    final Map<String, Widget Function()> matchStateWidgets = {
      "Live": _buildLiveStatus,
      "Complete": _buildCompleteStatus,
      "Upcoming": _buildUpcomingStatus,
      "Starting": _buildStartingStatus
    };

    // Get widget based on current matchState, fallback to "Upcoming"
    return matchStateWidgets[widget.matchState]?.call() ??
        matchStateWidgets["Starting"]!();
  }

  Widget _buildLiveStatus() {
    return Row(
      children: [
        SvgPicture.asset(
          'lib/UI_V1/assets/icons/live-icon.svg',
          width: 24,
          height: 24,
        ),
        const SizedBox(width: 8),
        const Text(
          'Live',
          style: TextStyle(
            color: Color(0xFFC32C1B),
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildCompleteStatus() {
    DateTime dateTime =
        DateTime.fromMillisecondsSinceEpoch(int.parse(widget.startDate));
    return Text(DateFormat('d MMMM y').format(dateTime));
  }

  Widget _buildUpcomingStatus() {
    return Text(
      '${widget.remainingTime.inHours}h ${widget.remainingTime.inMinutes.remainder(60)}m ${widget.remainingTime.inSeconds.remainder(60)}s',
      style: TextStyle(color: Colors.orange.shade400),
    );
  }

  Widget _buildStartingStatus() {
    return Text('Upcoming', style: TextStyle(color: Colors.orange.shade400));
  }
}
