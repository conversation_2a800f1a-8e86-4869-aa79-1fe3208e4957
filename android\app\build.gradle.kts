import java.util.Properties
import java.io.FileNotFoundException

plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
    id("com.google.gms.google-services")
}

val localPropertiesFile = File(rootProject.projectDir, "customLocal.properties")
val properties = Properties()

if (localPropertiesFile.exists()) {
    localPropertiesFile.reader(Charsets.UTF_8).use { reader ->
        properties.load(reader)
    }
} else {
    throw FileNotFoundException("Flutter SDK not found. Define location with flutter.sdk in the customLocal.properties file.")
}

val flutterRoot = properties.getProperty("flutter.sdk")
val flutterVersionCode = properties.getProperty("flutter.versionCode")?.toIntOrNull() ?: 1
val flutterVersionName = properties.getProperty("flutter.versionName") ?: "1.0.0"

android {
    namespace = "com.top3"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        applicationId = "com.top3"
        minSdk = 23  // Updated from flutter.minSdkVersion to 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutterVersionCode.toInt()
        versionName = flutterVersionName
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("debug")
            isMinifyEnabled = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }

    applicationVariants.all {
        val variant = this
        outputs.all {
            val output = this as com.android.build.gradle.internal.api.BaseVariantOutputImpl
            outputFileName = "Top3-Alpha-V${variant.versionName}.${variant.versionCode}.apk"
        }
    }

    lintOptions {
        disable("MissingPermission")
        // Or more specifically:
        // ignore("MissingPermission")
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation("org.jetbrains.kotlin:kotlin-stdlib:2.0.0")
    implementation("com.google.firebase:firebase-auth")
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.1.4")
    implementation(platform("com.google.firebase:firebase-bom:33.13.0"))
    implementation("com.google.firebase:firebase-analytics")

    // Use the newer Play Services libraries instead of Play Core
    implementation("com.google.android.play:app-update:2.1.0")
    implementation("com.google.android.play:app-update-ktx:2.1.0")
    implementation("com.google.android.play:feature-delivery:2.1.0")
    implementation("com.google.android.play:feature-delivery-ktx:2.1.0")
    implementation("com.google.android.play:core-common:2.0.3")
}

// Update versionCode in customLocal.properties after the build
gradle.buildFinished {
    properties.setProperty("flutter.versionCode", (flutterVersionCode.toInt() + 1).toString())
    localPropertiesFile.writer(Charsets.UTF_8).use { writer ->
        properties.store(writer, "Auto-incremented versionCode")
    }
}
