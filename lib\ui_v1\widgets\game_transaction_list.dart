import 'package:flutter/material.dart';
import '../../backend/model/game_transaction.dart';
import 'cards/game_transaction_card.dart';
import 'utils/paginated_list_interface.dart';

class GameTransactionList extends PaginatedListInterface<GameTransaction> {
  GameTransactionList({
    super.key,
    required super.list,
    required super.emptyTitle,
    required super.onRefresh,
  }) : super(scrollController: ScrollController());

  @override
  _GameTransactionListState createState() => _GameTransactionListState();
}

class _GameTransactionListState
    extends PaginatedListInterfaceState<GameTransaction, GameTransactionList>
    with SingleTickerProviderStateMixin {
  @override
  TickerProvider get vsyncProvider => this;

  @override
  Future<List<GameTransaction>> getMoreItems() async {
    return Future.value(List<GameTransaction>.empty());
  }

  @override
  Widget buildListItem(BuildContext context, int index) {
    return Padding(
      padding: EdgeInsets.all(6),
      child: GameTransactionCard(transaction: widget.list[index]),
    );
  }
}
