import 'dart:convert';

import 'base_transaction.dart';

/// Represents a bank-related transaction such as deposits or withdrawals.
///
/// Contains details about the payment instrument, status, and other bank-specific information.
class BankTransaction extends BaseTransaction {
  /// Unique identifier for the transaction
  final String transactionId;

  /// Whether the transaction was successful
  final bool isSuccessful;

  /// Response code from the payment processor
  final String responseCode;

  /// Response message from the payment processor
  final String responseMessage;

  /// Current state of the transaction (e.g., completed, pending, failed)
  final String transactionState;

  /// Type of payment instrument used (e.g., UPI, credit card)
  final String paymentMethod;

  /// Creates a new BankTransaction instance
  BankTransaction({
    required super.userId,
    required super.timeStamp,
    required super.paymentType,
    required super.transactionAmount,
    required this.transactionId,
    required bool status,
    required String code,
    required String message,
    required String state,
    required String paymentInstrument,
  })  : isSuccessful = status,
        responseCode = code,
        responseMessage = message,
        transactionState = state,
        paymentMethod = paymentInstrument;

  /// Getter for backward compatibility
  bool get status => isSuccessful;

  /// Getter for backward compatibility
  String get code => responseCode;

  /// Getter for backward compatibility
  String get message => responseMessage;

  /// Getter for backward compatibility
  String get state => transactionState;

  /// Getter for backward compatibility
  String get paymentInstrument => paymentMethod;

  /// Creates a BankTransaction instance from a JSON map
  factory BankTransaction.fromJson(Map<String, dynamic> json) {
    final paymentDetails =
        Map<String, dynamic>.from(jsonDecode(json['paymentDetails']));
    final data = Map<String, dynamic>.from(paymentDetails['data']);
    final paymentInstrument =
        Map<String, dynamic>.from(data['paymentInstrument']);

    return BankTransaction(
      userId: json['userId'],
      timeStamp: json['timeStamp'],
      paymentType: json['paymentType'],
      transactionAmount: data['amount'],
      transactionId: data['merchantTransactionId'],
      status: paymentDetails['success'],
      code: paymentDetails['code'],
      message: paymentDetails['message'],
      state: data['state'],
      paymentInstrument: paymentInstrument['type'],
    );
  }

  /// Converts this BankTransaction to a JSON map
  @override
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'timeStamp': timeStamp,
      'amount': amount,
      'paymentType': paymentType,
      'transactionId': transactionId,
      'status': status,
      'code': code,
      'message': message,
      'state': state,
      'paymentInstrument': paymentInstrument,
    };
  }
}
