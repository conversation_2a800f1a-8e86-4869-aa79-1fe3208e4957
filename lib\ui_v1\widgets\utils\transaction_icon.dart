import 'package:flutter/material.dart';

class TransactionIcon extends StatelessWidget {
  final String paymentType;

  const TransactionIcon({
    Key? key,
    required this.paymentType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Icon(
      paymentType.toLowerCase() == 'credit'
          ? Icons.call_received
          : Icons.call_made,
      color: paymentType.toLowerCase() == 'credit' ? Colors.green : Colors.red,
      size: 24,
    );
  }
}
