# Top3 - Fantasy Sports App

Top3 is a fantasy sports application built with Flutter that allows users to select their top 3 players for upcoming matches and join contests to win prizes based on player performance.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Project Structure](#project-structure)
- [Security Features](#security-features)
- [Getting Started](#getting-started)
- [Dependencies](#dependencies)
- [Building and Running](#building-and-running)
- [Building Secure Release](#building-secure-release)

## Overview

Top3 is a fantasy sports platform where users can:
- Select their top 3 players for upcoming matches
- Join contests with different entry fees
- Win prizes based on player performance
- Track their contest rankings and history
- Manage their wallet for deposits and withdrawals

The app provides a seamless experience for sports enthusiasts to engage with their favorite matches by predicting the top-performing players.

## Features

### User Authentication
- Google Sign-In integration
- Phone number authentication
- Secure token management

### Match and Contest Management
- Browse upcoming and live matches
- View available contests with different entry fees
- Join multiple contests for the same match
- Select top 3 players for each contest

### Wallet and Transactions
- Secure wallet for managing funds
- Payment integration (PhonePe, Google Pay, etc.)
- Transaction history
- Withdrawal functionality

### User Profile
- User statistics and performance history
- Joined contests tracking
- Notification center

### UI/UX
- Dark and light theme support
- Responsive design for different screen sizes
- Smooth animations and transitions

## Architecture

The application follows a layered architecture pattern:

### Presentation Layer (UI)
- Flutter widgets organized by feature
- Theme management with provider pattern
- Route management for navigation

### Business Logic Layer
- Service classes for business logic
- State management using Provider
- Dependency injection with GetIt

### Data Layer
- Repository pattern for data access
- API services for remote data
- Local storage for caching and user preferences

### Core Layer
- Utilities and helpers
- Security features
- Configuration management
- Error handling

## Project Structure

```
lib/
├── auth/                  # Authentication related code
├── backend/
│   ├── model/             # Data models
│   └── rest/              # API services
├── core/
│   ├── config/            # App configuration
│   ├── error/             # Error handling
│   ├── network/           # Network utilities
│   ├── security/          # Security features
│   └── utils/             # Utility classes
├── ui_v1/
│   ├── assets/            # UI assets
│   ├── routes/            # Route definitions
│   ├── screens/           # App screens
│   ├── themes/            # Theme definitions
│   ├── utils/             # UI utilities
│   └── widgets/           # Reusable widgets
├── injection_container.dart # Dependency injection setup
└── main.dart              # Application entry point
```

## Security Features

The app implements several security measures to protect user data and prevent reverse engineering:

### Code Obfuscation
- Dart code obfuscation enabled during build
- ProGuard/R8 for Android native code obfuscation

### Device Security
- Root/jailbreak detection
- Emulator detection
- App tampering detection

### Secure Storage
- Encrypted local storage for sensitive data
- Integrity verification for stored data

### Secure Configuration
- AES-256 encrypted configuration files for release builds
- Different configurations for debug and release builds
- Automated encryption during secure build process
- Device-specific decryption keys for enhanced security
- Multi-layered obfuscation to resist reverse engineering
- Integrity verification with SHA-256 checksums

## Getting Started

### Prerequisites
- Flutter SDK (^3.7.2)
- Dart SDK (^3.7.2)
- Android Studio / VS Code
- Firebase project setup

### Setup
1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Configure Firebase:
   - Add your `google-services.json` for Android
   - Add your `GoogleService-Info.plist` for iOS
4. Configure API endpoints in the configuration files

## Dependencies

Key dependencies include:
- **firebase_core, firebase_auth**: For Firebase integration
- **google_sign_in**: For Google authentication
- **cloud_firestore**: For cloud database
- **http**: For API requests
- **provider**: For state management
- **get_it**: For dependency injection
- **shared_preferences**: For local storage
- **flutter_local_notifications**: For notifications
- **dio**: For HTTP client
- **internet_connection_checker**: For network connectivity

## Building and Running

### Development Build
```bash
flutter run
```

### Release Build
```bash
flutter build apk --release
flutter build ios --release
```

## Building Secure Release

To build a secure release version with all security features enabled:

```bash
# On Windows
./build_secure_app.bat

# On macOS/Linux
./build_secure_app.sh
```

This script will:
1. Clean the build directory
2. Get all dependencies
3. Encrypt the configuration file for secure storage
4. Build the app with Dart code obfuscation enabled
5. Split debug information for crash reporting
6. Apply ProGuard rules for Android


curl --location 'https://api-preprod.phonepe.com/apis/pg-sandbox/checkout/v2/order/OMO2505251417136955219119/status?details=true' --header 'Content-Type: application/json' --header 'Authorization: O-Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHBpcmVzT24iOjE3NDgxODYyMzM2OTcsIm1lcmNoYW50SWQiOiJURVNULU0yMkZUVE1FS1YyOEwiLCJtZXJjaGFudE9yZGVySWQiOiIxNzQ4MTYyODMzMzMzSFJyTE1pQkdFbVNibDlIUURhd0V4YWJNaGJxMSJ9.Yvgr5N5ssSJ4PdBuMo1VBsze24OGzXt8TUfmRJXJZmU'

curl --location 'https://api-preprod.phonepe.com/apis/pg-sandbox/checkout/v2/order/OMO2505251417136955219119/status?details=true' --header 'Content-Type: application/json' --header 'Authorization: O-Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHBpcmVzT24iOjE3NDgxNjY0MzExODQsIm1lcmNoYW50SWQiOiJURVNULU0yMkZUVE1FS1YyOEwifQ.YddJjj1yzXd5EqRLl4tmwdwOq7E2tVxC-XhNQkhJUCY'

curl --location 'https://api-preprod.phonepe.com/apis/pg-sandbox/checkout/v2/order/1748162833333HRrLMiBGEmSbl9HQDawExabMhbq1/status?details=true' --header 'Content-Type: application/json' --header 'Authorization: O-Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHBpcmVzT24iOjE3NDgxODYyMzM2OTcsIm1lcmNoYW50SWQiOiJURVNULU0yMkZUVE1FS1YyOEwiLCJtZXJjaGFudE9yZGVySWQiOiIxNzQ4MTYyODMzMzMzSFJyTE1pQkdFbVNibDlIUURhd0V4YWJNaGJxMSJ9.Yvgr5N5ssSJ4PdBuMo1VBsze24OGzXt8TUfmRJXJZmU'

curl --location 'https://api-preprod.phonepe.com/apis/pg-sandbox/checkout/v2/order/1748162833333HRrLMiBGEmSbl9HQDawExabMhbq1/status?details=true' --header 'Content-Type: application/json' --header 'Authorization: O-Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHBpcmVzT24iOjE3NDgxNjY0MzExODQsIm1lcmNoYW50SWQiOiJURVNULU0yMkZUVE1FS1YyOEwifQ.YddJjj1yzXd5EqRLl4tmwdwOq7E2tVxC-XhNQkhJUCY'