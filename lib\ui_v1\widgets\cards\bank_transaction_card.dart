import 'package:flutter/material.dart';
import '../../../backend/model/bank_transaction.dart';
import '../../themes/app_colors.dart';
import '../utils/transaction_details_dialog.dart';
import '../utils/transaction_icon.dart';
class BankTransactionCard extends StatelessWidget {
  final BankTransaction transaction;

  const BankTransactionCard({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: AppColors.of(context).cardBackground,
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: ListTile(
        leading: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              transaction.status ? Icons.check_circle : Icons.error,
              color: transaction.status ? Colors.green : Colors.red,
            ),
          ],
        ),
        title: Row(
          children: [
            Text(
              'Amount: ₹${transaction.amount / 100}  ',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        subtitle: Text(
          transaction.status ? 'Success' : 'Failed',
          style: TextStyle(
            color: transaction.status ? Colors.green : Colors.red,
          ),
        ),
        trailing: TransactionIcon(paymentType: transaction.paymentType),
        onTap: () {
          _showTransactionDetails(context, transaction);
        },
      ),
    );
  }

  void _showTransactionDetails(
      BuildContext context, BankTransaction transaction) {
    showDialog(
      context: context,
      builder: (context) => TransactionDetailsDialog(
          transactionId: transaction.transactionId,
          timeStamp: transaction.timeStamp,
          success: transaction.status,
          amount: transaction.amount,
          paymentType: transaction.paymentType),
    );
  }
}
