import 'package:flutter/material.dart';
import '../../../backend/model/match.dart';

abstract class Contests extends StatelessWidget {
  final Match? match;

  const Contests({super.key, this.match});

  @override
  Widget build(BuildContext context) {
    DateTime currentTime = DateTime.now(); // Current time
    Duration difference =
        DateTime.fromMillisecondsSinceEpoch(int.parse(match!.startDate))
            .difference(currentTime); // Remaining time

    // Calculate remaining hours, minutes, and seconds
    int hours = difference.inHours;
    int minutes = difference.inMinutes.remainder(60);
    int seconds = difference.inSeconds.remainder(60);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "${match?.team1 ?? "Unknown"} vs ${match?.team2 ?? "Unknown"}",
            ),
            Text(
              '${hours}h ${minutes}m ${seconds}s left',
              style: const TextStyle(
                fontSize: 14,
                color: Color.fromARGB(137, 0, 0, 0),
              ),
            ),
          ],
        ),
      ),
      body: buildBody(context),
    );
  }

  Widget buildBody(BuildContext context);
}
