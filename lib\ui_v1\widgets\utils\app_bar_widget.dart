import 'package:flutter/material.dart'; 
import '../../themes/app_colors.dart';
import '../../utils/constants.dart';

class AppBarWidget extends StatelessWidget implements PreferredSizeWidget {
  final Widget title;
  final bool showTabBar;

  const AppBarWidget({
    super.key,
    required this.title,
    this.showTabBar = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.of(context).cardBackground,
      leading: IconButton(
        icon: const Icon(Icons.menu),
        onPressed: () {
          Scaffold.of(context).openDrawer();
        },
      ),
      title: title,
      actions: <Widget>[
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () {
            Navigator.pushNamed(context, RoutePaths.notifications);
          },
        ),
        IconButton(
          icon: const Icon(Icons.account_balance_wallet_outlined),
          onPressed: () {
            Navigator.pushNamed(context, RoutePaths.wallet);
          },
        ),
      ],
      bottom: showTabBar
          ? const TabBar(
              indicatorColor: Color.fromARGB(
                  255, 48, 0, 180), // Color for the underline indicator
              labelColor: Color.fromARGB(
                  255, 48, 0, 180), // Color for the selected tab label
              unselectedLabelColor:
                  Colors.grey, // Color for the unselected tab labels
              tabs: [
                Tab(text: 'Live'),
                Tab(text: 'Upcoming'),
                Tab(text: 'Completed'),
              ],
            )
          : null,
    );
  }

  @override
  Size get preferredSize {
    // Return height based on whether the TabBar is shown
    return Size.fromHeight(kToolbarHeight + (showTabBar ? 48.0 : 0.0));
  }
}
