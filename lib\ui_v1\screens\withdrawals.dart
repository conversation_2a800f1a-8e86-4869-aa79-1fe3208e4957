import 'package:flutter/material.dart';

import '../themes/app_colors.dart';
import '../widgets/utils/big_blue_button.dart';


class Withdrawals extends StatefulWidget {
  const Withdrawals({super.key});

  @override
  _WithdrawalsState createState() => _WithdrawalsState();
}

class _WithdrawalsState extends State<Withdrawals> {
  String? selectedBank;
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller.addListener(_formatInput);
  }

  void _formatInput() {
    String text = _controller.text.replaceAll("₹ ", "").trim();
    _controller.value = TextEditingValue(
      text: text.isNotEmpty ? "₹ $text" : "",
      selection: TextSelection.collapsed(
          offset: text.isNotEmpty ? text.length + 2 : 0),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text('Withdrawals'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(size.width * 0.04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBalanceContainer(size),
              SizedBox(height: size.height * 0.01),
              _buildWithdrawInput(size),
              SizedBox(height: size.height * 0.01),
              _buildBankSelection(size),
              SizedBox(height: size.height * 0.03),
              BigBlueButton(
                icon: null,
                text: 'Withdraw Amount',
                onPressed: () {},
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBalanceContainer(Size size) {
    return Container(
      padding: EdgeInsets.all(size.width * 0.04),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.lightGreen, Colors.green],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(size.width * 0.03),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '💵 Withdrawable Balance',
            style: TextStyle(color: Colors.white, fontSize: size.width * 0.04),
          ),
          Container(
            padding: EdgeInsets.symmetric(
                horizontal: size.width * 0.03, vertical: size.width * 0.015),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(size.width * 0.05),
            ),
            child: Text(
              '₹ 29',
              style:
                  TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildWithdrawInput(Size size) {
    return _buildCard(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text('Withdraw',
              style: TextStyle(
                fontSize: size.width * 0.045,
                fontWeight: FontWeight.w500,
              )),
          TextField(
            controller: _controller,
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            style: TextStyle(
              fontSize: size.width * 0.08,
              fontWeight: FontWeight.bold,
            ),
            decoration: InputDecoration(
              hintText: '₹ 00',
              border: InputBorder.none,
            ),
          ),
          Divider(),
          Text(
            'Minimum ₹ 60',
            style: TextStyle(
              fontSize: size.width * 0.035,
              color: Colors.black38,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBankSelection(Size size) {
    return _buildCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Send Winnings to",
            style: TextStyle(
                fontSize: size.width * 0.045, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: size.height * 0.01),
          SizedBox(
            height: size.height * 0.27,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildBankTile(
                    bankName: "HDFC Bank",
                    accountNumber: "XXXXXXXXXXXX7155",
                    isSelected: selectedBank == "HDFC Bank",
                    onTap: () => setState(() => selectedBank = "HDFC Bank"),
                    size: size,
                  ),
                  Divider(),
                  _buildBankTile(
                      bankName: "Axis Bank",
                      accountNumber: "XXXXXXXXXXXX6784",
                      isSelected: selectedBank == "Axis Bank",
                      onTap: () => setState(() => selectedBank = "Axis Bank"),
                      size: size),
                ],
              ),
            ),
          ),
          TextButton(
            onPressed: () {},
            child: Text("+ Add Bank Account",
                style: TextStyle(
                    fontSize: size.width * 0.04, color: Colors.deepPurple)),
          ),
        ],
      ),
    );
  }

  Widget _buildCard({required Widget child}) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }

  Widget _buildBankTile(
      {required String bankName,
      required String accountNumber,
      required bool isSelected,
      required VoidCallback onTap,
      required Size size}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.deepPurple.withOpacity(0.05)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: ListTile(
          visualDensity: VisualDensity(horizontal: -4),
          leading: Icon(Icons.account_balance,
              color: isSelected
                  ? Colors.deepPurple
                  : AppColors.of(context).cardText),
          title: Text(bankName, style: TextStyle(fontWeight: FontWeight.bold)),
          subtitle: Text(accountNumber, style: TextStyle(color: Colors.grey)),
          trailing: SizedBox(
            width: size.width * 0.05,
            child: Radio<String>(
              value: bankName,
              groupValue: selectedBank,
              onChanged: (value) => setState(() => selectedBank = value!),
              activeColor: Colors.deepPurple,
            ),
          ),
        ),
      ),
    );
  }
}
