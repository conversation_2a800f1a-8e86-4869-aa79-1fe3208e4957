import 'package:flutter/material.dart';
import 'dart:math' as math;

class SwiggyStyleRefresh extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double triggerDistance;
  final Color? backgroundColor;

  const SwiggyStyleRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.triggerDistance = 120.0,
    this.backgroundColor,
  });

  @override
  State<SwiggyStyleRefresh> createState() => _SwiggyStyleRefreshState();
}

class _SwiggyStyleRefreshState extends State<SwiggyStyleRefresh>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pullController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pullAnimation;

  double _pullDistance = 0.0;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pullController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _pullAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pullController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pullController.dispose();
    super.dispose();
  }

  Widget _buildCricketBall() {
    return AnimatedBuilder(
      animation: Listenable.merge([_rotationAnimation, _pullAnimation]),
      builder: (context, child) {
        final scale = 0.6 + (_pullAnimation.value * 0.4);
        return Transform.scale(
          scale: scale,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Container(
              width: 45,
              height: 45,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: const RadialGradient(
                  colors: [
                    Color(0xFFDC143C), // Crimson red
                    Color(0xFF8B0000), // Dark red
                  ],
                  stops: [0.3, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: CustomPaint(
                painter: CricketBallPainter(),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handlePanStart(DragStartDetails details) {
    if (!_isRefreshing) {
      setState(() {
        _pullDistance = 0.0;
      });
    }
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    if (!_isRefreshing && details.delta.dy > 0) {
      // More aggressive pull detection for scrollable content
      setState(() {
        _pullDistance += details.delta.dy * 1.2; // Increased sensitivity
        _pullDistance = _pullDistance.clamp(0.0, widget.triggerDistance * 1.2);
      });

      final progress = (_pullDistance / widget.triggerDistance).clamp(0.0, 1.0);
      _pullController.value = progress;
    }
  }

  void _handlePanEnd(DragEndDetails details) {
    if (!_isRefreshing) {
      if (_pullDistance >= widget.triggerDistance) {
        _triggerRefresh();
      } else {
        _resetPull();
      }
    }
  }

  void _triggerRefresh() async {
    setState(() {
      _isRefreshing = true;
    });

    _rotationController.repeat();
    _pullController.forward();

    try {
      await widget.onRefresh();
    } finally {
      _rotationController.stop();
      _rotationController.reset();
      _resetPull();
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  void _resetPull() {
    _pullController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _pullDistance = 0.0;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: widget.onRefresh,
      displacement: widget.triggerDistance,
      backgroundColor: Colors.transparent,
      color: Colors.transparent,
      strokeWidth: 0,
      child: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
          // Handle overscroll for scrollable content
          if (notification is OverscrollNotification && !_isRefreshing) {
            if (notification.overscroll < 0) {
              // User is pulling down at the top
              final overscrollAmount = notification.overscroll.abs();
              setState(() {
                _pullDistance = overscrollAmount * 0.8;
                _pullDistance = _pullDistance.clamp(0.0, widget.triggerDistance * 1.2);
              });

              final progress = (_pullDistance / widget.triggerDistance).clamp(0.0, 1.0);
              _pullController.value = progress;
            }
          } else if (notification is ScrollEndNotification && !_isRefreshing) {
            _resetPull();
          }
          return false;
        },
        child: GestureDetector(
          onPanStart: _handlePanStart,
          onPanUpdate: _handlePanUpdate,
          onPanEnd: _handlePanEnd,
          behavior: HitTestBehavior.translucent,
          child: Container(
            color: Colors.transparent,
            child: Stack(
              children: [
                // Main content with elastic transform - THE SWIGGY/ZOMATO EFFECT
                AnimatedBuilder(
                  animation: _pullAnimation,
                  builder: (context, child) {
                    final translateY = _pullDistance * 0.8; // Elastic effect
                    return Transform.translate(
                      offset: Offset(0, translateY),
                      child: widget.child,
                    );
                  },
                ),

                // Cricket ball indicator
                if (_pullDistance > 15 || _isRefreshing)
                  AnimatedBuilder(
                    animation: _pullAnimation,
                    builder: (context, child) {
                      final opacity = _pullAnimation.value;
                      final ballY = (_pullDistance * 0.4) - 25;

                      return Positioned(
                        top: math.max(15, ballY),
                        left: MediaQuery.of(context).size.width / 2 - 22.5,
                        child: Opacity(
                          opacity: opacity.clamp(0.0, 1.0),
                          child: _buildCricketBall(),
                        ),
                      );
                    },
                  ),

                // Pull instruction text
                if (_pullDistance > 30 && !_isRefreshing)
                  AnimatedBuilder(
                    animation: _pullAnimation,
                    builder: (context, child) {
                      final opacity = (_pullAnimation.value * 1.5).clamp(0.0, 1.0);
                      final textY = (_pullDistance * 0.4) + 30;

                      return Positioned(
                        top: math.max(50, textY),
                        left: 0,
                        right: 0,
                        child: Opacity(
                          opacity: opacity,
                          child: Center(
                            child: Text(
                              _pullDistance >= widget.triggerDistance
                                  ? '🏏 Release to refresh'
                                  : '🏏 Pull down to refresh',
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class CricketBallPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw the main seam line
    final seamPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Main seam - a more realistic curved line
    final seamPath = Path();
    seamPath.moveTo(center.dx - radius * 0.7, center.dy - radius * 0.4);
    seamPath.quadraticBezierTo(
      center.dx - radius * 0.2, center.dy - radius * 0.1,
      center.dx, center.dy,
    );
    seamPath.quadraticBezierTo(
      center.dx + radius * 0.2, center.dy + radius * 0.1,
      center.dx + radius * 0.7, center.dy + radius * 0.4,
    );

    canvas.drawPath(seamPath, seamPaint);

    // Draw stitching marks along the seam
    final stitchPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1.8
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Left side stitches - more realistic pattern
    for (int i = 0; i < 8; i++) {
      final t = i / 7.0;
      final seamX = center.dx - radius * 0.7 + (radius * 0.7 * t);
      final seamY = center.dy - radius * 0.4 + (radius * 0.4 * t);

      // Perpendicular stitches
      final angle = math.atan2(radius * 0.4, radius * 0.7);
      final perpAngle = angle + math.pi / 2;

      final stitchLength = 4.0;
      final startX = seamX - math.cos(perpAngle) * stitchLength;
      final startY = seamY - math.sin(perpAngle) * stitchLength;
      final endX = seamX + math.cos(perpAngle) * stitchLength;
      final endY = seamY + math.sin(perpAngle) * stitchLength;

      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        stitchPaint,
      );
    }

    // Right side stitches
    for (int i = 0; i < 8; i++) {
      final t = i / 7.0;
      final seamX = center.dx + (radius * 0.7 * t);
      final seamY = center.dy + (radius * 0.4 * t);

      // Perpendicular stitches
      final angle = math.atan2(radius * 0.4, radius * 0.7);
      final perpAngle = angle + math.pi / 2;

      final stitchLength = 4.0;
      final startX = seamX - math.cos(perpAngle) * stitchLength;
      final startY = seamY - math.sin(perpAngle) * stitchLength;
      final endX = seamX + math.cos(perpAngle) * stitchLength;
      final endY = seamY + math.sin(perpAngle) * stitchLength;

      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        stitchPaint,
      );
    }

    // Add some additional detail lines for more realism
    final detailPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.7)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Secondary seam lines
    final secondarySeam1 = Path();
    secondarySeam1.moveTo(center.dx - radius * 0.5, center.dy - radius * 0.6);
    secondarySeam1.quadraticBezierTo(
      center.dx - radius * 0.1, center.dy - radius * 0.2,
      center.dx + radius * 0.1, center.dy + radius * 0.1,
    );
    canvas.drawPath(secondarySeam1, detailPaint);

    final secondarySeam2 = Path();
    secondarySeam2.moveTo(center.dx + radius * 0.1, center.dy - radius * 0.1);
    secondarySeam2.quadraticBezierTo(
      center.dx + radius * 0.1, center.dy + radius * 0.2,
      center.dx + radius * 0.5, center.dy + radius * 0.6,
    );
    canvas.drawPath(secondarySeam2, detailPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
