@echo off
echo Building secure app with obfuscation enabled...

echo Cleaning the build...
call flutter clean
if %ERRORLEVEL% NEQ 0 (
    echo Error during flutter clean
    pause
    exit /b %ERRORLEVEL%
)

echo Getting dependencies...
call flutter pub get
if %ERRORLEVEL% NEQ 0 (
    echo Error during flutter pub get
    pause
    exit /b %ERRORLEVEL%
)

echo Encrypting configuration file...
call dart encrypt_config.dart
if %ERRORLEVEL% NEQ 0 (
    echo Error during configuration encryption
    pause
    exit /b %ERRORLEVEL%
)

echo Building Android APK with obfuscation...
call flutter build apk --obfuscate --split-debug-info=build/debug-info --release
if %ERRORLEVEL% NEQ 0 (
    echo Error during flutter build
    pause
    exit /b %ERRORLEVEL%
)

REM The following command is for macOS only
REM flutter build ios --obfuscate --split-debug-info=build/debug-info --release

echo Build completed successfully!
echo The obfuscated APK is located at: build/app/outputs/flutter-apk/app-release.apk

echo Running the app...
call flutter run --release
if %ERRORLEVEL% NEQ 0 (
    echo Error running the app
    pause
    exit /b %ERRORLEVEL%
)

pause