import 'package:flutter/material.dart';
import '../../../backend/model/contest.dart';
import '../../../backend/model/player.dart';
import '../../../backend/model/match.dart';
import '../../utils/constants.dart';
import '../utils/top3_widget.dart';
import '../utils/contest_app_bar.dart';

class JoinedContestCard extends StatefulWidget {
  final String indianRupeeSymbol;
  final double screenWidth;
  final double screenHeight;
  final String pool;
  final String firstPrize;
  final String secondPrize;
  final List<Player> top3List;
  final bool saved;
  final Function(List<Player>) onTop3ListChanged;
  final Contest contest;
  final Match? match;
  final Duration? remainingTime;

  const JoinedContestCard({
    super.key,
    required this.indianRupeeSymbol,
    required this.screenWidth,
    required this.screenHeight,
    required this.pool,
    required this.firstPrize,
    required this.secondPrize,
    required this.top3List,
    required this.saved,
    required this.onTop3ListChanged,
    required this.contest,
    required this.match,
    required this.remainingTime,
  });

  @override
  State<JoinedContestCard> createState() => _JoinedContestCardState();
}

class _JoinedContestCardState extends State<JoinedContestCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(context, RoutePaths.rankScreen,
              arguments: {widget.match!.matchId, widget.contest.entry});
        },
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(widget.screenWidth * 0.02),
              ),
              elevation: 4,
              child: Padding(
                padding: EdgeInsets.all(widget.screenWidth * 0.02),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.all(widget.screenWidth * 0.02),
                      child: Text(
                        'Entry Fee ${widget.indianRupeeSymbol} ${widget.contest.entry}',
                        style: TextStyle(
                          fontSize: widget.screenWidth * 0.045,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(height: widget.screenHeight * 0.01),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: widget.screenWidth * 0.02),
                      child: Row(
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.join_full_outlined,
                                color: Color.fromARGB(97, 0, 9, 12),
                              ),
                              SizedBox(width: widget.screenWidth * 0.01),
                              Text(
                                'Pool: ${widget.indianRupeeSymbol} ${widget.pool}',
                                style: TextStyle(
                                  fontSize: widget.screenWidth * 0.035,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(width: widget.screenWidth * 0.035),
                          Row(
                            children: [
                              const Icon(
                                Icons.emoji_events,
                                color: Color.fromARGB(255, 253, 213, 12),
                              ),
                              SizedBox(width: widget.screenWidth * 0.01),
                              Text(
                                '${widget.indianRupeeSymbol} ${widget.firstPrize}',
                                style: TextStyle(
                                  fontSize: widget.screenWidth * 0.035,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(width: widget.screenWidth * 0.035),
                          Row(
                            children: [
                              const Icon(
                                Icons.emoji_events_outlined,
                                color: Color.fromARGB(255, 174, 152, 74),
                              ),
                              SizedBox(width: widget.screenWidth * 0.01),
                              Text(
                                '${widget.indianRupeeSymbol} ${widget.secondPrize}',
                                style: TextStyle(
                                    fontSize: widget.screenWidth * 0.035),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: widget.screenWidth * 0.02),
                      child: const Divider(
                        thickness: 1,
                        color: Colors.grey,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 35, bottom: 10, right: 10),
                      child: Row(
                        children: [
                          const Text(
                            'My Top3',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          widget.remainingTime! > Duration.zero
                              ? GestureDetector(
                                  onTap: () {
                                    Navigator.pushNamed(
                                      context,
                                      RoutePaths.showContest,
                                      arguments: {
                                        widget.contest,
                                        widget.match,
                                        ContestAppBar(
                                          widget.match,
                                        ),
                                        widget.top3List
                                      },
                                    );
                                  },
                                  child: const Row(
                                    children: [
                                      Icon(
                                        Icons.edit,
                                        size: 16,
                                        color: Color.fromARGB(255, 48, 0, 180),
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        'Edit',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color:
                                              Color.fromARGB(255, 48, 0, 180),
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ],
                      ),
                    ),
                    Top3Widget(
                      top3List: widget.top3List,
                      saved: widget.saved,
                      showPoints: true,
                      onTop3ListChanged: widget.onTop3ListChanged,
                    ),
                    const SizedBox(
                      height: 15,
                    )
                  ],
                ),
              ),
            ),
            widget.remainingTime! < Duration.zero
                ? Positioned(
                    bottom: -10,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: AnimatedBuilder(
                        animation: _controller,
                        builder: (context, child) {
                          return ShaderMask(
                            shaderCallback: (rect) {
                              return LinearGradient(
                                colors: [
                                  Colors.white.withOpacity(0.1),
                                  Colors.white.withOpacity(0.7),
                                  Colors.white.withOpacity(0.1),
                                ],
                                stops: const [0.0, 0.5, 1.0],
                                begin: Alignment(-5 + _controller.value * 5, 0),
                                end: Alignment(2 + _controller.value * 2, 0),
                              ).createShader(Rect.fromLTWH(
                                0,
                                0,
                                rect.width,
                                rect.height,
                              ));
                            },
                            blendMode: BlendMode.srcATop,
                            child: Container(
                              height: 32,
                              width: 150,
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [
                                    Color.fromARGB(255, 48, 0, 180),
                                    Color.fromARGB(255, 30, 0, 106),
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              child: const Center(
                                child: Text(
                                  'Winnings: 100',
                                  style: TextStyle(
                                      color: Color.fromARGB(255, 255, 242, 0)),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        ),
      ),
    );
  }
}
