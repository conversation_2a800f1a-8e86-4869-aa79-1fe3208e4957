import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import '../utils/logger.dart';

class SecureStorage {
  static const String _securePrefix = 'secure_';
  static const String _ivPrefix = 'iv_';
  static const String _checksumPrefix = 'checksum_';

  static final List<int> _encryptionKey = List.generate(32, (i) => i * 3 + 5);

  static Future<bool> secureStore(String key, String value) async {
    try {
      if (value.isEmpty) {
        Logger.warn('Attempted to store empty value for key: $key');
        return false;
      }

      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String secureKey = _securePrefix + key;
      final String ivKey = _ivPrefix + key;
      final String checksumKey = _checksumPrefix + key;

      final List<int> iv = List.generate(16, (_) => DateTime.now().millisecondsSinceEpoch % 255);

      final String encryptedData = _encrypt(value, iv);

      final String checksum = _calculateChecksum(encryptedData);

      await prefs.setString(secureKey, encryptedData);
      await prefs.setString(ivKey, base64Encode(iv));
      await prefs.setString(checksumKey, checksum);

      return true;
    } catch (e) {
      Logger.error('Error in secure storage', error: e);
      return false;
    }
  }

  static Future<String?> secureRetrieve(String key) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String secureKey = _securePrefix + key;
      final String ivKey = _ivPrefix + key;
      final String checksumKey = _checksumPrefix + key;

      final String? encryptedData = prefs.getString(secureKey);
      final String? ivString = prefs.getString(ivKey);
      final String? storedChecksum = prefs.getString(checksumKey);

      if (encryptedData == null || ivString == null || storedChecksum == null) {
        return null;
      }

      final String calculatedChecksum = _calculateChecksum(encryptedData);
      if (calculatedChecksum != storedChecksum) {
        Logger.warn('Data integrity check failed for key: $key');
        return null;
      }

      final List<int> iv = base64Decode(ivString);
      return _decrypt(encryptedData, iv);
    } catch (e) {
      Logger.error('Error retrieving secure data', error: e);
      return null;
    }
  }

  static Future<bool> secureDelete(String key) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String secureKey = _securePrefix + key;
      final String ivKey = _ivPrefix + key;
      final String checksumKey = _checksumPrefix + key;

      await prefs.remove(secureKey);
      await prefs.remove(ivKey);
      await prefs.remove(checksumKey);

      return true;
    } catch (e) {
      Logger.error('Error deleting secure data', error: e);
      return false;
    }
  }

  static String _encrypt(String data, List<int> iv) {
    final List<int> dataBytes = utf8.encode(data);
    final List<int> keyWithIv = [..._encryptionKey, ...iv];

    final List<int> encrypted = List<int>.filled(dataBytes.length, 0);
    for (int i = 0; i < dataBytes.length; i++) {
      encrypted[i] = dataBytes[i] ^ keyWithIv[i % keyWithIv.length];
    }

    return base64Encode(encrypted);
  }

  static String _decrypt(String encryptedData, List<int> iv) {
    final List<int> dataBytes = base64Decode(encryptedData);
    final List<int> keyWithIv = [..._encryptionKey, ...iv];

    final List<int> decrypted = List<int>.filled(dataBytes.length, 0);
    for (int i = 0; i < dataBytes.length; i++) {
      decrypted[i] = dataBytes[i] ^ keyWithIv[i % keyWithIv.length];
    }

    return utf8.decode(decrypted);
  }

  static String _calculateChecksum(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}
