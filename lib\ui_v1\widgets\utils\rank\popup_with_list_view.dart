import 'package:flutter/material.dart';

class PopupWithListView extends StatelessWidget {
  const PopupWithListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Popup Example")),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return _buildPopupDialog(context);
              },
            );
          },
          child: const Text("Show Popup"),
        ),
      ),
    );
  }

  // Method to build the Popup Dialog
  Widget _buildPopupDialog(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Close button at the top right
          Align(
            alignment: Alignment.topRight,
            child: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                Navigator.of(context).pop(); // Close the popup
              },
            ),
          ),
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'Select an Item',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          // ListView Builder inside the Popup
          SizedBox(
            height: 300, // Define height for the ListView
            child: ListView.builder(
              itemCount: 10, // Replace with the actual item count
              itemBuilder: (BuildContext context, int index) {
                return ListTile(
                  title: Text('Item ${index + 1}'),
                  onTap: () {
                    // Handle item click
                    print('Tapped on Item ${index + 1}');
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
