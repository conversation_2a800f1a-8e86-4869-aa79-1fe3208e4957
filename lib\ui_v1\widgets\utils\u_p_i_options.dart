// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'ui_v1/utils/add_amount_button.dart';

// @Deprecated('Delete this')
// class UPIOptions extends StatefulWidget {
//   final TextEditingController textEditingController;
//   const UPIOptions(this.textEditingController, {super.key});

//   @override
//   _UPIOptionsState createState() => _UPIOptionsState();
// }

// class _UPIOptionsState extends State<UPIOptions> {
//   bool _upiAppExpanded = false;
//   String PayByAnyUPIApp = 'Pay By Any UPI App';
//   final Map<String, String> _upiAppIcons = {
//     'Google Pay': 'lib/UI_V1/assets/icons/google-pay-icon.svg',
//     'PhonePe': 'lib/UI_V1/assets/icons/phonepe-icon.svg',
//     'Paytm': 'lib/UI_V1/assets/icons/paytm-icon.svg',
//   };
//   final TextEditingController _textEditingController = TextEditingController();
//   void _toggleUPIAppExpansion() {
//     setState(() {
//       _upiAppExpanded = !_upiAppExpanded;
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16.0),
//       child: ExpansionPanelList(
//         expansionCallback: (int index, bool isExpanded) {
//           _toggleUPIAppExpansion();
//         },
//         children: [
//           ExpansionPanel(
//             headerBuilder: (BuildContext context, bool isExpanded) {
//               return ListTile(
//                 leading: SvgPicture.asset(
//                   'lib/UI_V1/assets/icons/upi-icon.svg',
//                   width: 24,
//                   height: 24,
//                 ),
//                 title: Text(PayByAnyUPIApp),
//               );
//             },
//             body: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 ..._upiAppIcons.entries.map((entry) {
//                   return ListTile(
//                     leading: SvgPicture.asset(
//                       entry.value,
//                       width: 24,
//                       height: 24,
//                     ),
//                     title: Text(entry.key),
//                     trailing: SizedBox(
//                       width: 140, // Adjust the width as needed
//                       child: Padding(
//                         padding: const EdgeInsets.all(3),
//                         child: AddAmountButton(
//                           textEditingController: widget.textEditingController,
//                           onPressed: () {
//                             setState(() {});
//                           },
//                         ),
//                       ),
//                     ),
//                   );
//                 }),
//                 Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 16.0),
//                   child: Column(
//                     children: [
//                       const Divider(
//                         thickness: 1,
//                         color: Colors.grey,
//                       ),
//                       const SizedBox(height: 16),
//                       TextFormField(
//                         controller: _textEditingController,
//                         keyboardType: TextInputType.text,
//                         decoration: const InputDecoration(
//                           labelText: 'Enter UPI ID',
//                           border: OutlineInputBorder(),
//                           contentPadding:
//                               EdgeInsets.symmetric(horizontal: 12, vertical: 6),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 const SizedBox(height: 20), // Add some spacing at the bottom
//                 AddAmountButton(
//                   textEditingController: widget.textEditingController,
//                   onPressed: () {
//                     print(1);
//                   },
//                 ),
//                 const SizedBox(height: 16),
//               ],
//             ),
//             isExpanded: _upiAppExpanded,
//           ),
//         ],
//       ),
//     );
//   }
// }
