{"buildFiles": ["D:\\FLUTTER\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Git\\flutter\\top3_v1\\android\\app\\.cxx\\Debug\\6o5s27p2\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Git\\flutter\\top3_v1\\android\\app\\.cxx\\Debug\\6o5s27p2\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}