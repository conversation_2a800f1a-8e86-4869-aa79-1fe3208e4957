import 'package:flutter/material.dart';

class MenuItemCard extends StatelessWidget {
  final IconData? icon;
  final String? text;
  final VoidCallback? onTap;
  final Widget? trailing;
  final Widget? child;

  const MenuItemCard({
    Key? key,
    this.icon,
    this.text,
    this.onTap,
    this.trailing,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    final double height = MediaQuery.of(context).size.height;
    final double padding = width * 0.04;
    final double cardHeight = height * 0.07;
    final double iconSize = width * 0.065;

    return Card(
      elevation: 0,
      margin: EdgeInsets.symmetric(
          horizontal: padding * 0.5, vertical: padding * 0.25),
      shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(padding)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(padding),
        child: Container(
          // Removed the Expanded from here
          constraints: BoxConstraints(minHeight: cardHeight),
          padding: EdgeInsets.symmetric(horizontal: padding * 0.5),
          child: Row(
            children: [
              if (icon != null) ...[
                Icon(icon, size: iconSize),
                SizedBox(width: padding * 0.5),
              ],
              if (text != null)
                Text(
                  text!,
                  style: TextStyle(fontSize: width * 0.04),
                  overflow: TextOverflow.ellipsis,
                ),
              if (child != null) Expanded(child: child!) else Spacer(),
              if (trailing != null)
                Padding(
                  padding: EdgeInsets.only(left: padding),
                  child: SizedBox(
                    width: width * 0.2,
                    height: height * 0.04,
                    child: trailing,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
