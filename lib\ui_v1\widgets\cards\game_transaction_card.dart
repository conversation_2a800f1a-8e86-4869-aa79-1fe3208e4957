import 'package:flutter/material.dart';
import '../../../backend/model/game_transaction.dart';
import '../../themes/app_colors.dart';
import '../utils/transaction_details_dialog.dart';
import '../utils/transaction_icon.dart';

class GameTransactionCard extends StatelessWidget {
  final GameTransaction transaction;

  const GameTransactionCard({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return GestureDetector(
        onTap: () {
          _showTransactionDetails(context, transaction);
        },
        child: Card(
          color: AppColors.of(context).cardBackground,
          elevation: 4,
          margin: const EdgeInsets.all(0),
          child: Padding(
            padding: EdgeInsets.only(
              left: screenWidth * 0.03,
              right: screenWidth * 0.03,
              top: screenHeight * 0.01,
              bottom: screenHeight * 0.01,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${transaction.match.matchType} ${transaction.match.matchFormat}",
                        style: TextStyle(
                            color: Color.fromARGB(255, 113, 113, 113)),
                      ),
                      SizedBox(height: screenHeight * 0.01),
                      _buildTeamsSection(screenWidth, screenHeight),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      DateTime.fromMillisecondsSinceEpoch(
                              int.parse(transaction.match.startDate))
                          .toString()
                          .substring(0, 16),
                      style: TextStyle(
                          fontSize: screenWidth * 0.035, color: Colors.grey),
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Row(
                      children: [
                        Text(
                          "₹ ${transaction.amount}",
                          style: TextStyle(fontSize: screenWidth * 0.04),
                        ),
                        SizedBox(width: screenWidth * 0.05),
                        TransactionIcon(paymentType: transaction.paymentType),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }

  void _showTransactionDetails(
      BuildContext context, GameTransaction transaction) {
    showDialog(
      context: context,
      builder: (context) => TransactionDetailsDialog(
          transactionId: transaction.contestId,
          timeStamp: transaction.timeStamp,
          success: true,
          amount: transaction.amount,
          paymentType: transaction.paymentType),
    );
  }

  Widget _buildTeamsSection(double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.015),
      child: Row(
        children: [
          _buildTeamSection(screenWidth, transaction.match.team1ImagePath,
              transaction.match.team1),
          const Text('VS'),
          _buildTeamSection(screenWidth, transaction.match.team2ImagePath,
              transaction.match.team2, true),
        ],
      ),
    );
  }

  Widget _buildTeamSection(double screenWidth, String imageUrl, String teamName,
      [bool isReversed = false]) {
    List<Widget> widgets = [
      CircleAvatar(
        radius: screenWidth * 0.04,
        backgroundImage: NetworkImage(
            "https://top3-images.s3.ap-south-1.amazonaws.com/cricket/flags/$imageUrl"),
      ),
      SizedBox(width: screenWidth * 0.01),
      Text(
        teamName,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      SizedBox(width: screenWidth * 0.03)
    ];
    if (isReversed) {
      widgets = widgets.reversed.toList();
    }
    return Row(children: widgets);
  }
}
