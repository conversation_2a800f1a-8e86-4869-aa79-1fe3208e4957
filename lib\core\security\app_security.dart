import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../utils/logger.dart';

class AppSecurity {
  static const MethodChannel _channel = MethodChannel('com.top3/security');
  static bool _securityInitialized = false;

  static Future<void> initialize() async {
    if (_securityInitialized) return;

    try {
      final bool deviceCompromised = await _isDeviceCompromised();
      if (deviceCompromised) {
        Logger.warn('Device appears to be compromised (rooted/jailbroken)');
      }

      final bool runningOnEmulator = await _isRunningOnEmulator();
      if (runningOnEmulator && !kDebugMode) {
        Logger.warn('App is running on an emulator');
      }

      final bool appTampered = await _isAppTampered();
      if (appTampered) {
        Logger.error('App appears to be tampered with');
      }

      _securityInitialized = true;
      Logger.info('Security checks completed');
    } catch (e) {
      Logger.error('Error initializing security measures', error: e);
    }
  }

  static Future<bool> _isDeviceCompromised() async {
    if (kIsWeb) return false;

    try {
      if (Platform.isAndroid) {
        return await _isAndroidRooted();
      } else if (Platform.isIOS) {
        return await _isIosJailbroken();
      }
      return false;
    } catch (e) {
      Logger.error('Error checking device compromise status', error: e);
      return false;
    }
  }

  static Future<bool> _isAndroidRooted() async {
    try {
      final bool? result = await _channel.invokeMethod<bool>('isDeviceRooted');
      if (result == true) return true;

      if (await _checkForSUBinary() || await _checkForRootApps() || await _checkForRWPaths()) {
        return true;
      }

      return false;
    } catch (e) {
      return await _checkForSUBinary() || await _checkForRootApps() || await _checkForRWPaths();
    }
  }

  static Future<bool> _checkForSUBinary() async {
    final List<String> paths = [
      '/system/bin/su',
      '/system/xbin/su',
      '/sbin/su',
      '/system/su',
      '/system/bin/.ext/su',
      '/system/usr/we-need-root/su'
    ];

    for (final String path in paths) {
      try {
        final file = File(path);
        if (await file.exists()) {
          return true;
        }
      } catch (_) {
      }
    }

    return false;
  }

  static Future<bool> _checkForRootApps() async {
    final List<String> rootApps = [
      'com.noshufou.android.su',
      'com.thirdparty.superuser',
      'eu.chainfire.supersu',
      'com.topjohnwu.magisk'
    ];

    try {
      final bool? result = await _channel.invokeMethod<bool>(
        'checkPackagesInstalled',
        {'packages': rootApps}
      );
      return result ?? false;
    } catch (_) {
      return false;
    }
  }

  static Future<bool> _checkForRWPaths() async {
    final List<String> paths = [
      '/system',
      '/system/bin',
      '/system/sbin',
      '/system/xbin',
      '/vendor/bin',
      '/sbin',
      '/etc'
    ];

    for (final String path in paths) {
      try {
        final directory = Directory(path);
        final testFile = File('$path/test_rw');
        if (await directory.exists()) {
          try {
            await testFile.writeAsString('test');
            await testFile.delete();
            return true;
          } catch (_) {
          }
        }
      } catch (_) {
      }
    }

    return false;
  }

  static Future<bool> _isIosJailbroken() async {
    try {
      final bool? result = await _channel.invokeMethod<bool>('isDeviceJailbroken');
      if (result == true) return true;

      if (await _checkForJailbreakFiles() || await _checkForSuspiciousApps() || await _canWriteToSystemPaths()) {
        return true;
      }

      return false;
    } catch (e) {
      return await _checkForJailbreakFiles() || await _checkForSuspiciousApps() || await _canWriteToSystemPaths();
    }
  }

  static Future<bool> _checkForJailbreakFiles() async {
    final List<String> paths = [
      '/Applications/Cydia.app',
      '/Library/MobileSubstrate/MobileSubstrate.dylib',
      '/bin/bash',
      '/usr/sbin/sshd',
      '/etc/apt',
      '/usr/bin/ssh',
      '/private/var/lib/apt'
    ];

    for (final String path in paths) {
      try {
        final file = File(path);
        if (await file.exists()) {
          return true;
        }
      } catch (_) {
      }
    }

    return false;
  }

  static Future<bool> _checkForSuspiciousApps() async {
    final List<String> apps = [
      'Cydia',
      'FakeCarrier',
      'Icy',
      'IntelliScreen',
      'SBSettings',
      'WinterBoard'
    ];

    try {
      final bool? result = await _channel.invokeMethod<bool>(
        'checkSuspiciousApps',
        {'apps': apps}
      );
      return result ?? false;
    } catch (_) {
      return false;
    }
  }

  static Future<bool> _canWriteToSystemPaths() async {
    try {
      final testFile = File('/private/jailbreak.txt');
      await testFile.writeAsString('test');
      await testFile.delete();
      return true;
    } catch (_) {
      return false;
    }
  }

  static Future<bool> _isRunningOnEmulator() async {
    if (kIsWeb) return false;

    try {
      final bool? result = await _channel.invokeMethod<bool>('isEmulator');
      return result ?? false;
    } catch (e) {
      if (Platform.isAndroid) {
        return await _isAndroidEmulator();
      } else if (Platform.isIOS) {
        return await _isIosSimulator();
      }
      return false;
    }
  }

  static Future<bool> _isAndroidEmulator() async {
    try {
      final String? brand = await _getProp('ro.product.brand');
      final String? model = await _getProp('ro.product.model');
      final String? manufacturer = await _getProp('ro.product.manufacturer');
      final String? hardware = await _getProp('ro.hardware');

      return (brand?.toLowerCase().contains('generic') ?? false) ||
             (model?.toLowerCase().contains('sdk') ?? false) ||
             (model?.toLowerCase().contains('emulator') ?? false) ||
             (model?.toLowerCase().contains('android sdk built for') ?? false) ||
             (manufacturer?.toLowerCase().contains('genymotion') ?? false) ||
             (hardware?.toLowerCase().contains('goldfish') ?? false) ||
             (hardware?.toLowerCase().contains('ranchu') ?? false);
    } catch (_) {
      return false;
    }
  }

  static Future<String?> _getProp(String property) async {
    try {
      final result = await _channel.invokeMethod<String>('getProp', {'prop': property});
      return result;
    } catch (_) {
      return null;
    }
  }

  static Future<bool> _isIosSimulator() async {
    try {
      if (Platform.isIOS) {
        final String? processInfo = await _getProcessInfo();
        return processInfo?.toLowerCase().contains('simulator') ?? false;
      }
      return false;
    } catch (_) {
      return false;
    }
  }

  static Future<String?> _getProcessInfo() async {
    try {
      final result = await _channel.invokeMethod<String>('getProcessInfo');
      return result;
    } catch (_) {
      return null;
    }
  }

  static Future<bool> _isAppTampered() async {
    if (kIsWeb) return false;

    try {
      final bool? result = await _channel.invokeMethod<bool>('verifyAppIntegrity');
      return result == false;
    } catch (e) {
      Logger.error('Error checking app integrity', error: e);
      return false;
    }
  }
}
