import 'dart:async';

import '../error/exceptions.dart';
import 'logger.dart';

class RetryHelper {
  static Future<T> retry<T>({
    required Future<T> Function() operation,
    int maxRetries = 3,
    bool Function(Exception)? retryIf,
    void Function(Exception, int)? onRetry,
    int delayFactor = 200,
  }) async {
    int attempts = 0;

    while (true) {
      try {
        attempts++;
        return await operation();
      } on Exception catch (e) {
        // Don't retry if we've reached the maximum number of retries
        if (attempts > maxRetries) {
          Logger.error('Max retries reached', error: e);
          rethrow;
        }

        // Don't retry if the retryIf function returns false
        if (retryIf != null && !retryIf(e)) {
          Logger.error('Not retrying based on retryIf condition', error: e);
          rethrow;
        }

        // Calculate delay using exponential backoff
        final delay = Duration(milliseconds: delayFactor * (1 << (attempts - 1)));

        // Call the onRetry callback if provided
        if (onRetry != null) {
          onRetry(e, attempts);
        }

        Logger.info('Retrying operation (attempt $attempts of $maxRetries) after ${delay.inMilliseconds}ms');
        await Future.delayed(delay);
      }
    }
  }

  /// Determine if an exception should be retried based on its type
  static bool shouldRetryException(Exception exception) {
    // Retry network exceptions but not auth or validation exceptions
    return exception is NetworkException ||
           (exception is ApiException &&
            (exception.statusCode == null ||
             exception.statusCode! >= 500));
  }
}
