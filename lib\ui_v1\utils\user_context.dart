import '../../auth/auth_details.dart';

/// Provides access to the current user's information throughout the application.
///
/// Maintains in-memory cache of user data loaded from persistent storage.
class UserContext {
  /// Current user's unique identifier
  static late String _userId;

  /// Current user's email address
  static late String _userEmail;

  /// Current user's display name
  static late String _userName;

  /// Current user's profile photo URL
  static late String _userPhotoUrl;

  /// Current user's account balance
  static late String _userBalance;

  /// Initializes the user context by loading data from persistent storage
  ///
  /// Should be called at application startup after authentication
  static Future<void> init() async {
    final loggedIn = await AuthDetails.getIfLoggedIn();
    if (loggedIn == true) {
      _userId = await AuthDetails.getUserId() ?? '';
      _userEmail = await AuthDetails.getUserEmail() ?? '';
      _userName = await AuthDetails.getUserName() ?? '';
      _userPhotoUrl = await AuthDetails.getUserPhotoUrl() ?? '';
      _userBalance = await AuthDetails.getUserBalance() ?? '';
    }
  }

  /// Gets the current user's ID
  static String get userId => _userId;

  /// Gets the current user's email address
  static String get userEmail => _userEmail;

  /// Gets the current user's display name
  static String get userName => _userName;

  /// Gets the current user's profile photo URL
  static String get userPhotoUrl => _userPhotoUrl;

  /// Gets the current user's account balance
  static String get userBalance => _userBalance;

  /// Updates the user's balance from persistent storage
  ///
  /// Call this after transactions that may affect the balance
  static Future<void> refreshBalance() async {
    _userBalance = await AuthDetails.getUserBalance() ?? _userBalance;
  }

  /// Updates the user's profile photo URL from persistent storage
  ///
  /// Call this after the user updates their profile photo
  static Future<void> refreshPhotoUrl() async {
    _userPhotoUrl = await AuthDetails.getUserPhotoUrl() ?? _userPhotoUrl;
  }
}
