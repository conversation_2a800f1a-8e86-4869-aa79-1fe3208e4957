import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../auth/auth.dart';
import '../utils/constants.dart';
import '../utils/user_context.dart';
import '../widgets/cards/menu_item_card.dart';
import '../utils/theme_provider.dart';

class SideMenu extends StatefulWidget {
  const SideMenu({super.key});

  @override
  _SideMenuState createState() => _SideMenuState();
}

class _SideMenuState extends State<SideMenu>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isMenuOpen = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void toggleDrawer() {
    if (_isMenuOpen) {
      _animationController.reverse();
    } else {
      _animationController.forward();
    }
    setState(() {
      _isMenuOpen = !_isMenuOpen;
    });
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
        body: Padding(
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.03,
        vertical: screenHeight * 0.06,
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: () {
                Navigator.pushNamed(context, RoutePaths.profileScreen,
                    arguments: {
                      'name': UserContext.userName,
                      'photoUrl': UserContext.userPhotoUrl,
                      'isCurrentUser': true,
                      'followers': 0,
                      'following': 0,
                      'balance': UserContext.userBalance,
                      'totalWinnings': 0,
                      'contestsJoined': 0
                    });
              },
              child: Row(
                children: [
                  CircleAvatar(
                    radius: screenHeight * 0.04,
                    backgroundImage: NetworkImage(UserContext.userPhotoUrl),
                  ),
                  SizedBox(width: screenWidth * 0.03),
                  Text(
                    UserContext.userName,
                    style: TextStyle(
                      fontSize: screenWidth * 0.05,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: screenHeight * 0.03),
            MenuItemCard(
              icon: Icons.account_balance_wallet_outlined,
              text: 'My Balance',
              trailing: buildAddCashButton(),
            ),
            MenuItemCard(
              icon: Icons.book_outlined,
              text: 'How to play',
              onTap: () {},
            ),
            MenuItemCard(
              icon: Icons.settings_outlined,
              text: 'My Info & Settings',
              onTap: () {
                Navigator.pushNamed(context, RoutePaths.infoAndSettings,
                    arguments: {
                      UserContext.userName,
                      UserContext.userId,
                      UserContext.userEmail,
                      true
                    });
              },
            ),
            MenuItemCard(
              icon: Icons.swap_horiz_outlined,
              text: 'Transactions',
              onTap: () {
                Navigator.pushNamed(
                  context,
                  RoutePaths.allTransactions,
                );
              },
            ),
            MenuItemCard(
              icon: Icons.input,
              text: 'Withdrawl',
              onTap: () {
                Navigator.pushNamed(
                  context,
                  RoutePaths.withdrawals,
                );
              },
            ),
            Theme(
              data:
                  Theme.of(context).copyWith(dividerColor: Colors.transparent),
              child: Column(
                children: [
                  MenuItemCard(
                    child: ExpansionTile(
                      tilePadding: EdgeInsets.zero,
                      visualDensity:
                          VisualDensity(horizontal: -4, vertical: -4),
                      leading: Icon(Icons.more_horiz_outlined),
                      title: Text("More"),
                      children: [
                        _buildListTile("Legality", () => {}),
                        _buildListTile("About Us", () => {}),
                        _buildListTile("Community Guidelines", () => {}),
                        _buildListTile("Terms and Conditions", () => {},
                            isLast: true),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Card(
              elevation: 4,
              margin: EdgeInsets.all(screenWidth * 0.02),
              child: Padding(
                padding: const EdgeInsets.all(0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CupertinoSegmentedControl<ThemeMode>(
                      children: {
                        ThemeMode.system: Text('System'),
                        ThemeMode.light: Text('Light'),
                        ThemeMode.dark: Text('Dark'),
                      },
                      groupValue: Provider.of<ThemeProvider>(context).themeMode,
                      onValueChanged: (ThemeMode mode) {
                        Provider.of<ThemeProvider>(context, listen: false)
                            .setThemeMode(mode);
                      },
                    ),
                  ],
                ),
              ),
            ),
            buildLogoutButton(),
          ],
        ),
      ),
    ));
  }

  Widget _buildListTile(String title, VoidCallback? onTap,
      {bool isLast = false}) {
    return Container(
      decoration: BoxDecoration(
        border: isLast
            ? null
            : Border(
                bottom: BorderSide(color: Colors.grey, width: 0.5),
              ),
      ),
      child: ListTile(
        onTap: onTap,
        dense: true,
        title: Text(title),
      ),
    );
  }

  Widget buildAddCashButton() {
    return ElevatedButton(
      onPressed: () {
        Navigator.pushNamed(context, RoutePaths.paymentScreen);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color.fromARGB(255, 221, 221, 255),
        padding: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * 0.03,
          vertical: MediaQuery.of(context).size.height * 0.01,
        ),
        shape: RoundedRectangleBorder(
          borderRadius:
              BorderRadius.circular(MediaQuery.of(context).size.width * 0.05),
        ),
        minimumSize: Size(0, MediaQuery.of(context).size.height * 0.03),
      ),
      child: Text(
        'Add Cash',
        style: TextStyle(
          fontSize: MediaQuery.of(context).size.width * 0.03,
          color: const Color.fromARGB(255, 48, 0, 180),
        ),
      ),
    );
  }

  Widget buildLogoutButton() {
    return ListTile(
      iconColor: Color.fromARGB(255, 189, 13, 0),
      leading: Icon(Icons.logout),
      title: Text(
        'Logout',
        style: TextStyle(
            color: Color.fromARGB(
              255,
              189,
              13,
              0,
            ),
            fontWeight: FontWeight.bold),
      ),
      onTap: () async {
        await Auth.signout();
        Navigator.pushNamed(context, RoutePaths.googleSignInScreen);
      },
    );
  }
}
