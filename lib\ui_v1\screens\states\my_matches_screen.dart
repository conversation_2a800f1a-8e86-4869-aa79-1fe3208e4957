import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
  import '../../../backend/model/match.dart';
import '../../../backend/rest/match_details.dart';
import '../../utils/user_context.dart';
import '../../widgets/my_matches/completed_match_list.dart';
import '../../widgets/my_matches/my_matches_list.dart';

class MyMatchesScreen extends StatefulWidget {
  const MyMatchesScreen({super.key});

  @override
  _MyMatchesScreenState createState() => _MyMatchesScreenState();
}

class _MyMatchesScreenState extends State<MyMatchesScreen> {
  List<Match> liveMatchList = [];
  List<Match> upcomingMatchList = [];
  List<Match> completedMatchList = [];

  @override
  void initState() {
    super.initState();
    fetchLiveMatches();
    fetchCompletedMatches();
  }

  Future<void> fetchLiveMatches() async {
    DateTime now = DateTime.now();
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    List<String>? matchesJson =
        sharedPreferences.getStringList('liveMatchesList');
    List<Match> fetchedList =
        matchesJson!.map((match) => Match.fromJson(jsonDecode(match))).toList();
    setState(() {
      liveMatchList = fetchedList
          .where((match) =>
              DateTime.fromMillisecondsSinceEpoch(int.parse(match.startDate))
                  .isBefore(now))
          .map((match) {
        if (match.matchState == "Upcoming") {
          match.matchState = "Starting";
        }
        return match;
      }).toList();
    });
    setState(() {
      upcomingMatchList = fetchedList
          .where((match) =>
              DateTime.fromMillisecondsSinceEpoch(int.parse(match.startDate))
                  .isAfter(now))
          .toList();
    });
  }

  Future<void> fetchCompletedMatches() async {
    List<Match> completedList =
        await MatchDetails.getUserCompletedMatchesByPage(UserContext.userId, 1);
    setState(() {
      completedMatchList = completedList;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: TabBarView(
        children: [
          MyMatchesList(
              list: liveMatchList,
              emptyTitle: "Oops! No live matches at the moment.",
              onRefresh: fetchLiveMatches), // Content for Live tab
          MyMatchesList(
              list: upcomingMatchList,
              emptyTitle:
                  "Oops! You don't have any upcoming matches, Please Join One",
              onRefresh: fetchLiveMatches), // Content for Upcoming tab
          CompletedMatchList(
              list: completedMatchList,
              emptyTitle: "Explore Top3, Join, win and chill",
              onRefresh: fetchCompletedMatches), // Content for Completed tab
        ],
      ),
    );
  }
}
