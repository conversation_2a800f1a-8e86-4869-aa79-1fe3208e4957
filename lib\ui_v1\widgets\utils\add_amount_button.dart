import 'package:flutter/material.dart';
import 'big_blue_button.dart';

class AddAmountButton extends StatefulWidget {
  final TextEditingController textEditingController;
  final VoidCallback onPressed;
  final ValueChanged<double> onValueSelected;

  const AddAmountButton(
      {Key? key,
      required this.textEditingController,
      required this.onPressed,
      required this.onValueSelected})
      : super(key: key);

  @override
  _AddAmountButtonState createState() => _AddAmountButtonState();
}

class _AddAmountButtonState extends State<AddAmountButton> {
  late String buttonText;
  late VoidCallback action;
  late Widget detailsView;

  @override
  void initState() {
    super.initState();
    _updateButtonText();
    widget.textEditingController.addListener(_updateButtonText);
  }

  @override
  void dispose() {
    widget.textEditingController.removeListener(_updateButtonText);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double padding = screenWidth * 0.05;
    return Padding(
      padding: EdgeInsets.all(padding),
      child: BigBlueButton(
        icon: null,
        text: buttonText,
        onPressed: action,
        onSplitPressed: detailsView,
      ),
    );
  }

  void _updateButtonText() {
    setState(() {
      String inputText = widget.textEditingController.text;

      if (inputText.isNotEmpty) {
        double originalAmount = double.tryParse(inputText) ?? 0.0;
        double gst = originalAmount * (1 - (1 / 1.28));
        String finalAmount = originalAmount.toStringAsFixed(2);
        buttonText = "Add ₹ $finalAmount";
        widget.onValueSelected(originalAmount);
        buildDetailedView(originalAmount, gst);
      } else {
        buttonText = "Add Amount";
        detailsView = SizedBox.shrink();
      }

      action = inputText.isNotEmpty ? widget.onPressed : () {};
    });
  }

  void buildDetailedView(double originalAmount, double gst) {
    double screenWidth = MediaQuery.of(context).size.width;
    double padding = screenWidth * 0.04;
    double borderRadius = screenWidth * 0.05;
    double dividerHeight = screenWidth * 0.05;

    detailsView = Card(
      margin: EdgeInsets.all(padding),
      elevation: 5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Deposit', originalAmount),
            SizedBox(height: screenWidth * 0.02),
            _buildDetailRow(
              'GST (28%)',
              gst,
              symbol: '-',
              color: Colors.red,
            ),
            Divider(thickness: 1, height: dividerHeight),
            _buildDetailRow(
              'Top3 Points ',
              gst,
              symbol: '+',
              color: Color.fromARGB(255, 0, 42, 92),
              tooltipMessage:
                  'Bonus points which can be used to join contests!',
            ),
            Divider(thickness: 1, height: dividerHeight),
            _buildDetailRow(
              'Total',
              originalAmount,
              isTotal: true,
              color: Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    double amount, {
    bool isTotal = false,
    String? symbol,
    Color? color,
    String? tooltipMessage,
  }) {
    double screenWidth = MediaQuery.of(context).size.width;
    double fontSizeLabel = screenWidth * 0.04;
    double fontSizeValue = screenWidth * 0.04;

    final TextStyle labelStyle = TextStyle(
      fontSize: isTotal ? fontSizeLabel * 1.2 : fontSizeLabel,
      fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
      color: isTotal ? Colors.black : Colors.grey[700],
    );

    final TextStyle valueStyle = TextStyle(
      fontSize: isTotal ? fontSizeValue * 1.2 : fontSizeValue,
      fontWeight: FontWeight.bold,
      color: color ?? Colors.black,
    );

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Text(label, style: labelStyle),
            if (tooltipMessage != null)
              Padding(
                padding: EdgeInsets.only(left: screenWidth * 0.01),
                child: Tooltip(
                  margin: EdgeInsets.all(8),
                  triggerMode: TooltipTriggerMode.tap,
                  message: tooltipMessage,
                  child: Icon(
                    Icons.info_outline,
                    size: screenWidth * 0.05,
                    color: Colors.grey[600],
                  ),
                ),
              ),
          ],
        ),
        Row(
          children: [
            if (symbol != null) Text(symbol, style: valueStyle),
            Text(' ₹${amount.toStringAsFixed(2)}', style: valueStyle),
          ],
        ),
      ],
    );
  }
}
