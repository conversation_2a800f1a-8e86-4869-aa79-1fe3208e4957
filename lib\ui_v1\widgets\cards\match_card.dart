import 'dart:async';

import 'package:flutter/material.dart';
import '../../../backend/model/match.dart';
import '../../../backend/rest/match_details.dart';
import '../../utils/image_helper.dart';
import '../utils/match_status_widget.dart';
import '../utils/timer/match_countdown_timer.dart';

class MatchCard extends StatefulWidget {
  final Match match;
  final Future<void> Function() onTap;
  final Color color;

  const MatchCard(
      {required this.match,
      required this.onTap,
      required this.color,
      super.key});

  @override
  _MatchCardState createState() => _MatchCardState();
}

class _MatchCardState extends State<MatchCard> {
  late Duration remainingTime;
  late Match match;
  late MatchCountdownTimer timer;
  bool _isDisabled = false;
  bool _isTimeInitialized = false;
  final int checkInterval = 5 * 60;
  DateTime now = DateTime.now();

  Future<void> _handleTap() async {
    if (_isDisabled) return;
    setState(() {
      _isDisabled = true;
    });
    try {
      await widget.onTap();
    } finally {
      _isDisabled = false;
    }
  }

  @override
  void initState() {
    super.initState();
    match = widget.match;
    remainingTime = Duration.zero;
    timer = MatchCountdownTimer(
        targetDateTime: match.startDate,
        onTick: (remainingTime) {
          setState(() {
            this.remainingTime = remainingTime;
            _isTimeInitialized = true;
          });
        },
        onEnd: () async {
          await _checkLiveStatus();
          if (!mounted) return;
          setState(() {
            if (match.matchState == "Upcoming" &&
                DateTime.fromMillisecondsSinceEpoch(int.parse(match.startDate))
                    .isBefore(now)) {
              match.matchState = "Starting";
            }
          });
        });
  }

  Future<bool> _checkLiveStatus() async {
    try {
      Match updatedMatch = await MatchDetails.getMatchDetails(match.matchId);
      setState(() {
        match.matchState = updatedMatch.matchState;
      });
      if (updatedMatch.matchState == "Live") {
        return true;
      }
    } catch (error) {
      debugPrint('Error fetching match status: $error');
    }
    return false;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    if (!_isTimeInitialized) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: _handleTap,
      child: Card(
        color: widget.color,
        elevation: 4,
        margin: EdgeInsets.zero,
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.01, vertical: screenHeight * 0.01),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("${match.matchType} ${match.matchFormat}"),
              SizedBox(height: screenHeight * 0.015),
              _buildTeamsSection(screenWidth),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTeamsSection(double screenWidth) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.01),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildTeamSection(match.team1ImagePath, match.team1),
          _buildMatchStatus(screenWidth),
          _buildTeamSection(match.team2ImagePath, match.team2, true),
        ],
      ),
    );
  }

  Widget _buildTeamSection(String imageUrl, String teamName,
      [bool isReversed = false]) {
    List<Widget> widgets = [
      ImageHelper.getCircleAvatar(
        imageUrl: ImageHelper.flagImageBaseUrl + imageUrl,
        radius: 24,
      ),
      const SizedBox(width: 5),
      Text(
        teamName,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
    ];

    if (isReversed) {
      widgets = widgets.reversed.toList();
    }

    return Row(children: widgets);
  }

  Widget _buildMatchStatus(double screenWidth) {
    match.contestCount;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        MatchStatusWidget(
            matchState: widget.match.matchState,
            startDate: widget.match.startDate,
            remainingTime: remainingTime),
        Text(
          '${match.contestCount} contests',
          style: TextStyle(
              fontWeight: FontWeight.bold, fontSize: screenWidth * 0.035),
        ),
      ],
    );
  }
}
