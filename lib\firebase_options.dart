// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyANAaVJQk868Mj1liG0m1Cxi0YrNP_jqfk',
    appId: '1:97309647468:web:e350c63ff9ea83b88b2e49',
    messagingSenderId: '97309647468',
    projectId: 'top3-510f2',
    authDomain: 'top3-510f2.firebaseapp.com',
    storageBucket: 'top3-510f2.appspot.com',
    measurementId: 'G-NZY8GXZFEV',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA-uGUqUsj64eAjpbh_Rxiw2oUeCWtXEqg',
    appId: '1:97309647468:android:644e8331413fb8568b2e49',
    messagingSenderId: '97309647468',
    projectId: 'top3-510f2',
    storageBucket: 'top3-510f2.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAKrDHITOkQStE5H7XSuJyFfaQGygxJcsQ',
    appId: '1:97309647468:ios:b259f8f88a08c1878b2e49',
    messagingSenderId: '97309647468',
    projectId: 'top3-510f2',
    storageBucket: 'top3-510f2.appspot.com',
    androidClientId: '97309647468-fnktfsgaqggo6nc1qa260u9jknmb5vr2.apps.googleusercontent.com',
    iosClientId: '97309647468-pvrmlu2dh2re1uj59nc15ijcu8rrmt2g.apps.googleusercontent.com',
    iosBundleId: 'com.top3',
  );
}
