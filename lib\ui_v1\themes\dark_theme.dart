import 'package:flutter/material.dart';
import 'app_theme.dart';
import 'app_colors.dart';

class DarkTheme extends AppTheme {
  @override
  ThemeData buildThemeData() {
    return ThemeData(
      brightness: Brightness.dark,
      cardTheme: getCardTheme(),
      // elevatedButtonTheme: getElevatedButtonTheme().copyWith(
      //   style: ButtonStyle(
      //     backgroundColor: MaterialStateProperty.all(Colors.deepPurple),
      //   ),
      // ),
      appBarTheme: AppBarTheme(
        color: const Color(0xFF121212), // Dark AppBar background
        elevation: 4,
        iconTheme: const IconThemeData(
          color: Colors.white,
        ),
        titleTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: 20,
        ),
      ),
      scaffoldBackgroundColor: const Color(0xFF121212),
      extensions: const [
        AppColors(
          cardBackground: Color(0xFF1E1E1E),
          cardText: Colors.white,
          buttonBackground: Colors.deepPurple,
          buttonText: Colors.white,
        ),
      ],
    );
  }
}
