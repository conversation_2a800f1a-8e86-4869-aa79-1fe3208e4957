import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../backend/model/match.dart';
import '../../themes/app_colors.dart';
import '../../utils/image_helper.dart';

class UpcomingMatchCard extends StatelessWidget {
  final Match match;
  final VoidCallback onTap;

  const UpcomingMatchCard(
      {super.key, required this.match, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return GestureDetector(
      onTap: onTap,
      child: Card(
        color: AppColors.of(context).cardBackground,
        elevation: 4,
        margin: const EdgeInsets.all(0),
        child: Padding(
          padding: EdgeInsets.only(
              left: screenWidth * 0.03,
              right: screenWidth * 0.03,
              top: screenHeight * 0.01,
              bottom: screenHeight * 0.027),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("${match.matchType} ${match.matchFormat}"),
              SizedBox(height: screenHeight * 0.01),
              _buildTeamsSection(screenWidth, screenHeight),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTeamsSection(double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.015),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildTeamSection(screenWidth, match.team1ImagePath, match.team1),
          _buildMatchStatus(screenWidth),
          _buildTeamSection(screenWidth, match.team2ImagePath, match.team2),
        ],
      ),
    );
  }

  Widget _buildMatchStatus(double screenWidth) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildMatchStatusIcon(),
        _buildMatchDateTime(screenWidth),
      ],
    );
  }

  Widget _buildMatchStatusIcon() {
    IconData? statusIcon =
        match.matchState.contains("live") ? Icons.live_tv : null;
    return statusIcon != null
        ? Icon(statusIcon, color: Colors.red)
        : const SizedBox();
  }

  Widget _buildMatchDateTime(double screenWidth) {
    DateTime date =
        DateTime.fromMillisecondsSinceEpoch(int.parse(match.startDate));
    String formattedDate = DateFormat("dd MMM yyyy").format(date);
    String formattedTime = DateFormat.jm().format(date);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(formattedDate,
            style: TextStyle(
                fontWeight: FontWeight.bold, fontSize: screenWidth * 0.04)),
        Text(formattedTime, style: TextStyle(fontSize: screenWidth * 0.045)),
      ],
    );
  }

  Widget _buildTeamSection(
      double screenWidth, String imageUrl, String teamName) {
    return Column(
      children: [
        ImageHelper.getCircleAvatar(
          imageUrl: ImageHelper.flagImageBaseUrl + imageUrl,
          radius: screenWidth * 0.07,
        ),
        SizedBox(height: screenWidth * 0.01),
        Text(
          teamName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }
}
