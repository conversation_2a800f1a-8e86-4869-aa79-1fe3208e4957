# Top3 App Security Guide

This document outlines the security measures implemented in the Top3 app to protect against reverse engineering and other security threats.

## Security Features

The app includes the following security features:

1. **Code Obfuscation**
   - Dart code obfuscation
   - ProGuard/R8 for Android native code obfuscation

2. **Root/Jailbreak Detection**
   - Detection of rooted Android devices
   - Detection of jailbroken iOS devices
   - Emulator detection

3. **Secure Configuration**
   - Sensitive API keys and secrets stored in obfuscated configuration files
   - Different configurations for debug and release builds

4. **Secure Storage**
   - Encrypted local storage for sensitive user data
   - Integrity verification for stored data

5. **Anti-Tampering Protection**
   - App integrity verification
   - Detection of modified app binaries

## Building a Secure Release

To build a secure release version of the app with all security features enabled, use the provided script:

```bash
# On Windows
./build_secure_app.bat

# On macOS/Linux
./build_secure_app.sh
```

This script will:
1. Clean the build directory
2. Get all dependencies
3. Encrypt the configuration file for secure storage
4. Build the app with Dart code obfuscation enabled
5. Split debug information for crash reporting
6. Apply ProGuard rules for Android

## Security Configuration

### Android

The Android build is configured with ProGuard rules in `android/app/proguard-rules.pro`. These rules:
- Obfuscate class and method names
- Strip debug information
- Protect against common reverse engineering techniques
- Preserve essential functionality

### iOS

For iOS builds, code obfuscation is enabled through the Flutter build process. Additional security measures are implemented in the native code.

### Dart Code

Dart code obfuscation is enabled during the build process with the `--obfuscate` flag. This makes the code significantly harder to reverse engineer.

## Secure Storage

Sensitive data is stored using the `SecureStorage` class, which:
- Encrypts data before storing
- Adds integrity verification
- Prevents unauthorized access

## Configuration Security

API keys and secrets are stored in:
- `assets/config/config_debug.json` for debug builds (unobfuscated)
- `assets/config/config.json` for release builds (encrypted)

The `AppConfig` class handles secure loading and decoding of these files. The encryption process is automated during the secure build process using the `encrypt_config.dart` script, which applies AES-256 encryption with the following security features:

- **AES-256 Encryption**: Industry-standard encryption algorithm
- **PBKDF2 Key Derivation**: Secure key generation from a passphrase
- **Random Salt and IV**: Unique for each encryption operation
- **Device-Specific Salt**: Makes the decryption key unique to each device
- **Integrity Verification**: SHA-256 checksum to detect tampering
- **Multi-layer Obfuscation**: Multiple layers of encoding and encryption
- **Code Obfuscation**: Decryption code is intentionally obfuscated to resist reverse engineering

## Security Checks

The `AppSecurity` class performs runtime security checks:
- Detects rooted/jailbroken devices
- Detects running on emulators
- Verifies app integrity

## Best Practices for Developers

1. **Never hardcode sensitive information** in the source code
2. **Use the secure storage utilities** for storing sensitive user data
3. **Add all API keys and secrets** to the configuration files
4. **Test security features** on both debug and release builds
5. **Keep dependencies updated** to address security vulnerabilities

## Security Limitations

While these measures significantly increase the difficulty of reverse engineering the app, they cannot provide absolute security. Determined attackers with sufficient resources may still be able to bypass some security measures.

## Reporting Security Issues

If you discover a security vulnerability, please report it responsibly by contacting the security team at [<EMAIL>](mailto:<EMAIL>).
