import 'dart:math';

import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../../backend/model/match.dart';
import '../../backend/rest/match_details.dart';
import '../themes/app_colors.dart';
import '../utils/constants.dart';
import '../utils/context.dart';
import '../utils/user_context.dart';
import 'cards/match_card.dart';

class CustomPageController extends PageController {
  CustomPageController({
    super.initialPage,
    super.keepPage,
    super.viewportFraction = 0.9,
  });
}

class JoinedMatches extends StatefulWidget {
  const JoinedMatches({super.key});

  @override
  JoinedMatchesState createState() => JoinedMatchesState();
}

class JoinedMatchesState extends State<JoinedMatches> {
  final PageController _pageController = CustomPageController();
  List<Match>? userMatchList;
  Future<void>? fetchContestsFuture; // Make this nullable

  @override
  void initState() {
    super.initState();
    fetchContestsFuture = fetchContests(); // Initialize the future
  }

  Future<void> fetchContests() async {
    try {
      // Use the improved Context class method to get live matches
      List<Match> fetchedList = await Context.getLiveMatches();

      // Update match states
      var filteredList = fetchedList.map((match) => updateMatchState(match)).toList();

      if (mounted) {
        setState(() {
          userMatchList = filteredList;
        });
      }
    } catch (e) {
      debugPrint("Error fetching contests: $e");
    }
  }

  Match updateMatchState(Match match) {
    final matchStartMillis = int.parse(match.startDate);
    if (matchStartMillis < DateTime.now().millisecondsSinceEpoch &&
        match.matchState == "Upcoming") {
      match.matchState = "Starting";
    }
    return match;
  }

  Future<void> _navigateToJoinedContests(BuildContext context, Match match) async {
    try {
      final BuildContext currentContext = context;
      Map<String, dynamic> map = await MatchDetails.getUserJoinedMatchContestDetails(
          UserContext.userId, match.matchId);

      if (mounted) {
        Navigator.pushNamed(currentContext, RoutePaths.joinedContests,
            arguments: {match, map});
      }
    } catch (e) {
      debugPrint("Error navigating to joined contests: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return FutureBuilder<void>(
      future: fetchContestsFuture, // Use the nullable future here
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return const Center(child: Text('Error loading matches'));
        } else if (userMatchList == null || userMatchList!.isEmpty) {
          return const SizedBox.shrink();
        } else if (userMatchList!.isNotEmpty) {
          List<Widget> matchCards =
              userMatchList!.take(min(userMatchList!.length, 8)).map((match) {
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: SizedBox(
                width: screenWidth * 0.8,
                child: MatchCard(
                  match: match,
                  onTap: () async {
                    await _navigateToJoinedContests(context, match);
                    return;
                  },
                  // color: const Color(0xFFE2E6F5),
                  color: AppColors.of(context).cardBackground,
                ),
              ),
            );
          }).toList();

          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              const Text(
                "My Contests",
                style: TextStyle(
                  fontSize: 20.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(
                height: screenHeight * 0.17,
                child: SingleChildScrollView(
                  controller: _pageController,
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: matchCards,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              SmoothPageIndicator(
                controller: _pageController,
                count: min(userMatchList!.length, 8),
                effect: const WormEffect(
                  dotWidth: 8,
                  dotHeight: 8,
                  spacing: 16,
                  dotColor: Colors.grey,
                  activeDotColor: Color.fromARGB(255, 79, 124, 161),
                ),
              ),
            ],
          );
        } else {
          return SizedBox.shrink();
        }
      },
    );
  }
}
