import '../../backend/model/bank_transaction.dart';
import '../../backend/model/base_transaction.dart';
import 'package:flutter/material.dart';
import '../../backend/model/game_transaction.dart';
import '../utils/user_context.dart';
import 'cards/game_transaction_card.dart';
import 'cards/bank_transaction_card.dart';
import '../../backend/rest/contest_details.dart';
import 'utils/paginated_list_interface.dart';

class TransactionList extends PaginatedListInterface<BaseTransaction> {
  final String selectedTransaction;

  TransactionList({
    super.key,
    required super.list,
    required super.emptyTitle,
    required super.onRefresh,
    required super.scrollController,
    required this.selectedTransaction,
  }) : super(shouldDisposeController: false);

  @override
  _TransactionListState createState() => _TransactionListState();
}

class _TransactionListState
    extends PaginatedListInterfaceState<BaseTransaction, TransactionList>
    with SingleTickerProviderStateMixin {
  @override
  TickerProvider get vsyncProvider => this;

  @override
  void initState() {
    super.initState();
  }

  @override
  Future<List<BaseTransaction>> getMoreItems() async {
    List<BaseTransaction> transactions =
        await ContestDetails.getAllTransactions(UserContext.userId, page);
    return transactions;
  }

  @override
  Widget buildListItem(BuildContext context, int index) {
    final transaction = widget.list[index];

    if (!shouldDisplayTransaction(transaction)) {
      return SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.all(4),
      child: transaction is BankTransaction
          ? BankTransactionCard(transaction: transaction)
          : GameTransactionCard(transaction: transaction as GameTransaction),
    );
  }

  bool shouldDisplayTransaction(BaseTransaction transaction) {
    if (widget.selectedTransaction == "All Transactions") {
      return true;
    }
    switch (widget.selectedTransaction) {
      case "Game Transactions":
        return transaction is GameTransaction;
      case "Bank Transactions":
        return transaction is BankTransaction;
      case "Withdrawals":
        return transaction is BankTransaction &&
            transaction.paymentType != "credit";
      default:
        return true;
    }
  }
}
