import 'package:flutter/material.dart';
import '../../../backend/model/match.dart';
import '../../../backend/rest/match_details.dart';
import '../../themes/app_colors.dart';
import '../../utils/constants.dart';
import '../../utils/user_context.dart';
import '../cards/match_card.dart';
import '../utils/paginated_list_interface.dart';

abstract class BaseMatchList extends PaginatedListInterface<Match> {
  BaseMatchList({
    super.key,
    required super.list,
    required super.emptyTitle,
    required super.onRefresh,
  }) : super(scrollController: ScrollController());
}

abstract class BaseMatchListState
    extends PaginatedListInterfaceState<Match, BaseMatchList> {
  @override
  Widget buildListItem(BuildContext context, int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20.0),
      child: SizedBox(
        height: MediaQuery.of(context).size.height * 0.14,
        child: MatchCard(
          match: widget.list[index],
          onTap: () async {
            Map<String, dynamic> map =
                await MatchDetails.getUserJoinedMatchContestDetails(
                    UserContext.userId, widget.list[index].matchId);
            Navigator.pushNamed(
              context,
              RoutePaths.joinedContests,
              arguments: {widget.list[index], map},
            );
          },
          color: AppColors.of(context).cardBackground,
        ),
      ),
    );
  }
}
