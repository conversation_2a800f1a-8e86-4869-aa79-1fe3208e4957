import 'package:flutter/material.dart';

import '../../themes/app_colors.dart';

class AmountTextBox extends StatefulWidget {
  final TextEditingController controller;
  final String labelText;

  const AmountTextBox({
    required this.controller,
    required this.labelText,
    super.key,
  });

  @override
  _AmountTextBoxState createState() => _AmountTextBoxState();
}

class _AmountTextBoxState extends State<AmountTextBox> {
  String _selectedValue = ''; // Default value is empty

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return Padding(
      padding: EdgeInsets.all(screenWidth * 0.04), // Add padding here
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            controller: widget.controller,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: widget.labelText,
              border: const OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.04,
                  vertical: screenHeight * 0.01),
              prefixText: '₹ ',
            ),
            maxLength: 7,
            onChanged: (value) {
              // Reset selected value when manually inputting text
              if (value != _selectedValue) {
                setState(() {
                  _selectedValue = '';
                });
              }
            },
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSelectableItem('20', screenWidth, screenHeight),
              _buildSelectableItem('50', screenWidth, screenHeight),
              _buildSelectableItem('100', screenWidth, screenHeight),
              _buildSelectableItem('150', screenWidth, screenHeight),
              _buildSelectableItem('200', screenWidth, screenHeight),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectableItem(
      String value, double screenWidth, double screenHeight) {
    bool isSelected = _selectedValue == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedValue = value;
          widget.controller.text = value;
        });
      },
      child: Container(
        width: screenWidth * 0.16,
        height: screenHeight * 0.04,
        margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.01),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(screenHeight * 0.02),
          border: Border.all(
            color: isSelected ? Colors.orange : Colors.grey,
            width: 2,
          ),
          color: isSelected ? Colors.orange : Colors.transparent,
        ),
        child: Center(
          child: Text(
            '₹$value',
            style: TextStyle(
              // color: isSelected ? Colors.white : Colors.black,
              // color: AppColors.of(context).buttonText,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }
}
