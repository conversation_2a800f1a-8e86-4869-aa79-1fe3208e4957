// import 'package:flutter/material.dart';
// import 'ui_v1/utils/add_amount_button.dart';

// @Deprecated('Delete this')
// class CardPaymentOptions extends StatefulWidget {
//   final TextEditingController textEditingController;
//   const CardPaymentOptions(this.textEditingController, {super.key});

//   @override
//   _CardPaymentOptionsState createState() => _CardPaymentOptionsState();
// }

// class _CardPaymentOptionsState extends State<CardPaymentOptions> {
//   bool _cardExpanded = false;
//   final TextEditingController _cardNumberController = TextEditingController();
//   final TextEditingController _nameOnCardController = TextEditingController();
//   final TextEditingController _expiryController = TextEditingController();
//   final TextEditingController _cvvController = TextEditingController();

//   void _toggleCardExpansion() {
//     setState(() {
//       _cardExpanded = !_cardExpanded;
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16.0),
//       child: ExpansionPanelList(
//         expansionCallback: (int index, bool isExpanded) {
//           _toggleCardExpansion();
//         },
//         children: [
//           ExpansionPanel(
//             headerBuilder: (BuildContext context, bool isExpanded) {
//               return const ListTile(
//                 leading: Icon(Icons.payment_outlined),
//                 title: Text('Debit/Credit Card'),
//               );
//             },
//             body: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Padding(
//                     padding: const EdgeInsets.all(16.0),
//                     child: Column(
//                       children: [
//                         TextFormField(
//                           controller: _cardNumberController,
//                           decoration:
//                               const InputDecoration(labelText: 'Card Number'),
//                         ),
//                         TextFormField(
//                           controller: _nameOnCardController,
//                           decoration:
//                               const InputDecoration(labelText: 'Name on Card'),
//                         ),
//                         const SizedBox(height: 16),
//                         Row(
//                           children: [
//                             Expanded(
//                               child: TextFormField(
//                                 controller: _expiryController,
//                                 decoration:
//                                     const InputDecoration(labelText: 'Expiry'),
//                               ),
//                             ),
//                             const SizedBox(width: 16),
//                             Expanded(
//                               child: TextFormField(
//                                 controller: _cvvController,
//                                 decoration:
//                                     const InputDecoration(labelText: 'CVV'),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ],
//                     )),
//                 const SizedBox(height: 30), // Add some spacing at the bottom
//                 AddAmountButton(
//                   textEditingController: widget.textEditingController,
//                   onPressed: () {},
//                 ),
//                 const SizedBox(height: 16)
//               ],
//             ),
//             isExpanded: _cardExpanded,
//           ),
//         ],
//       ),
//     );
//   }
// }
