import 'dart:convert';

import '../../core/utils/logger.dart';
import '../../ui_v1/utils/user_context.dart';
import '../model/match.dart';
import '../model/user.dart';
import '../model/player.dart';
import '../../core/error/exceptions.dart';
import 'api_service.dart';

class MatchDetails {
  static const apiUrl =
      'https://3dqho93s01.execute-api.ap-south-1.amazonaws.com/match-details';

  static List<Match> _parseMatches(List<Map<String, dynamic>> list) {
    try {
      return list.map((element) => Match.fromJson(element)).toList();
    } catch (e) {
      Logger.error('Failed to parse match data', error: e);
      throw JsonParseException('Invalid match data format');
    }
  }

  static Future<List<Match>> _fetchMatches(
      String functionName, Map<String, dynamic> body) async {
    try {
      final response = await ApiService.postRequest(apiUrl, functionName, body);

      if (response.body.isEmpty) {
        return [];
      }

      final dynamic decodedBody = jsonDecode(response.body);
      final List<Map<String, dynamic>> list = decodedBody is Iterable<dynamic>
          ? List<Map<String, dynamic>>.from(decodedBody)
          : [Map<String, dynamic>.from(decodedBody)];

      return _parseMatches(list);
    } on FormatException catch (e) {
      Logger.error('Failed to parse match data', error: e);
      throw JsonParseException('Invalid match data format');
    } catch (e) {
      Logger.error('Failed to fetch matches', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to fetch matches: ${e.toString()}');
    }
  }

  static Future<List<Match>> getUpcomingMatches(String userId) {
    if (userId.isEmpty) {
      throw ValidationException('User ID cannot be empty');
    }
    return _fetchMatches('getUpcomingMatchList', {'userId': userId});
  }

  static Future<List<Match>> getUserJoinedMatches(String userId) {
    if (userId.isEmpty) {
      throw ValidationException('User ID cannot be empty');
    }
    return _fetchMatches('getUserJoinedMatchList', {'userId': userId});
  }

  static Future<List<Match>> getAllUserJoinedMatches(String userId) {
    if (userId.isEmpty) {
      throw ValidationException('User ID cannot be empty');
    }
    return _fetchMatches('getAllUserJoinedMatchList', {'userId': userId});
  }

  static Future<List<Match>> getUserCompletedMatchesByPage(
      String userId, int page) {
    if (userId.isEmpty) {
      throw ValidationException('User ID cannot be empty');
    }
    if (page < 0) {
      throw ValidationException('Page number cannot be negative');
    }
    return _fetchMatches('getUserMatchList',
        {'userId': userId, 'matchState': 'Complete', 'page': page});
  }

  static Future<Map<String, int>> getUserContestCount(String userId) async {
    try {
      if (userId.isEmpty) {
        throw ValidationException('User ID cannot be empty');
      }

      final response = await ApiService.postRequest(
          apiUrl, 'getUserContestCount', {'userId': userId});

      return Map<String, int>.from(jsonDecode(response.body));
    } on FormatException catch (e) {
      Logger.error('Failed to parse contest count data', error: e);
      throw JsonParseException('Invalid contest count format');
    } catch (e) {
      Logger.error('Failed to get user contest count', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to get contest count: ${e.toString()}');
    }
  }

  static Future<Map<String, String>> getScoreCard(String matchId) async {
    try {
      if (matchId.isEmpty) {
        throw ValidationException('Match ID cannot be empty');
      }

      final response = await ApiService.postRequest(
          apiUrl, 'getScoreCard', {'matchId': matchId});

      return Map<String, String>.from(jsonDecode(response.body));
    } on FormatException catch (e) {
      Logger.error('Failed to parse score card data', error: e);
      throw JsonParseException('Invalid score card format');
    } catch (e) {
      Logger.error('Failed to get score card', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to get score card: ${e.toString()}');
    }
  }

  static Future<List<Player>> getPlayerDetails(String matchId) async {
    try {
      if (matchId.isEmpty) {
        throw ValidationException('Match ID cannot be empty');
      }

      final response = await ApiService.postRequest(
          apiUrl, 'getPlayerDetails', {'matchId': matchId});

      if (response.body.isEmpty) {
        return [];
      }

      final List<Map<String, dynamic>> resultList =
          List<Map<String, dynamic>>.from(jsonDecode(response.body));

      return resultList.map((element) {
        return Player(
          name: element["name"] ?? '',
          playerId: element["playerId"] ?? '',
          imagePath: element["imagePath"] ?? '',
          teamId: element["teamId"] ?? '',
          status: element["status"] ?? '',
        );
      }).toList();
    } on FormatException catch (e) {
      Logger.error('Failed to parse player details', error: e);
      throw JsonParseException('Invalid player details format');
    } catch (e) {
      Logger.error('Failed to get player details', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to get player details: ${e.toString()}');
    }
  }

  static Future<List<User>> getUserContestRank(
      String matchId, String contestEntry) async {
    try {
      if (matchId.isEmpty) {
        throw ValidationException('Match ID cannot be empty');
      }
      if (contestEntry.isEmpty) {
        throw ValidationException('Contest entry cannot be empty');
      }

      final response = await ApiService.postRequest(apiUrl, 'getUserContestRank',
          {"matchId": matchId, "contest": contestEntry});

      if (response.body.isEmpty) {
        return [];
      }

      List<User> userList = (jsonDecode(response.body) as List<dynamic>)
          .map((json) => User.fromJson(json as Map<String, dynamic>))
          .toList();

      userList.sort((a, b) {
        if (a.userId == UserContext.userId) return -1;
        if (b.userId == UserContext.userId) return 1;
        return a.rank.compareTo(b.rank);
      });

      return userList;
    } on FormatException catch (e) {
      Logger.error('Failed to parse user contest rank data', error: e);
      throw JsonParseException('Invalid user contest rank format');
    } catch (e) {
      Logger.error('Failed to get user contest rank', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to get user contest rank: ${e.toString()}');
    }
  }

  static Future<List<Map<String, dynamic>>> getMatchTop3PlayerDetails(
      String matchId) async {
    try {
      if (matchId.isEmpty) {
        throw ValidationException('Match ID cannot be empty');
      }

      final response = await ApiService.postRequest(
          apiUrl, 'getTop3PlayerDetails', {'matchId': matchId});

      if (response.body.isEmpty) {
        return [];
      }

      return List<Map<String, dynamic>>.from(jsonDecode(response.body));
    } on FormatException catch (e) {
      Logger.error('Failed to parse top 3 player details', error: e);
      throw JsonParseException('Invalid top 3 player details format');
    } catch (e) {
      Logger.error('Failed to get top 3 player details', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to get top 3 player details: ${e.toString()}');
    }
  }

  static Future<List<Map<String, dynamic>>> getTop3PlayerDetails(
      String userId, String matchId) async {
    try {
      if (userId.isEmpty) {
        throw ValidationException('User ID cannot be empty');
      }
      if (matchId.isEmpty) {
        throw ValidationException('Match ID cannot be empty');
      }

      final response = await ApiService.postRequest(apiUrl,
          'getUserTop3PlayerDetails', {'userId': userId, 'matchId': matchId});

      if (response.body.isEmpty) {
        return [];
      }

      return List<Map<String, dynamic>>.from(jsonDecode(response.body));
    } on FormatException catch (e) {
      Logger.error('Failed to parse user top 3 player details', error: e);
      throw JsonParseException('Invalid user top 3 player details format');
    } catch (e) {
      Logger.error('Failed to get user top 3 player details', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to get user top 3 player details: ${e.toString()}');
    }
  }

  static Future<Map<String, dynamic>> getUserJoinedMatchContestDetails(
      String userId, String matchId) async {
    try {
      if (userId.isEmpty) {
        throw ValidationException('User ID cannot be empty');
      }
      if (matchId.isEmpty) {
        throw ValidationException('Match ID cannot be empty');
      }

      final body = {"matchId": matchId, "userId": userId};
      final response = await ApiService.postRequest(
          apiUrl, 'getUserJoinedMatchContestDetails', body);

      return json.decode(response.body) as Map<String, dynamic>;
    } on FormatException catch (e) {
      Logger.error('Failed to parse user joined match contest details', error: e);
      throw JsonParseException('Invalid user joined match contest details format');
    } catch (e) {
      Logger.error('Failed to get user joined match contest details', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to get user joined match contest details: ${e.toString()}');
    }
  }

  static Future<Match> getMatchDetails(String matchId) async {
    try {
      if (matchId.isEmpty) {
        throw ValidationException('Match ID cannot be empty');
      }

      List<Match> matches =
          await _fetchMatches('getMatchDetails', {"matchId": matchId});

      if (matches.isEmpty) {
        throw ServerException('Match not found');
      }

      return matches[0];
    } catch (e) {
      Logger.error('Failed to get match details', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to get match details: ${e.toString()}');
    }
  }
}
