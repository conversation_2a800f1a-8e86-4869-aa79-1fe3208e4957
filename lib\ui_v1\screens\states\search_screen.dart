import 'dart:async';
import 'package:flutter/material.dart';
import '../../themes/app_colors.dart';
import '../../utils/constants.dart';
import '../../../backend/rest/user_details.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  _SearchScreenState createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _users = [];
  bool _isLoading = false;
  final FocusNode _focusNode = FocusNode();
  Timer? _debounce;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    });
  }

  void _onSearchChanged(String query) {
    if (query.isEmpty) {
      setState(() {
        _users = [];
      });
    } else {
      _debounce?.cancel();
      _debounce = Timer(Duration(milliseconds: 500), () {
        _fetchUsers(query);
      });
    }
  }

  Future<void> _fetchUsers(String search) async {
    setState(() => _isLoading = true);
    List<Map<String, dynamic>> results = await UserDetails.searchUsers(search);
    setState(() {
      _users = results;
      _isLoading = false;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double padding = screenWidth * 0.05;
    final double fieldWidth = screenWidth * 0.9;

    return Scaffold(
      body: Padding(
        padding:
            EdgeInsets.symmetric(horizontal: padding, vertical: padding / 2),
        child: Column(
          children: [
            SizedBox(
              width: fieldWidth,
              child: TextField(
                controller: _searchController,
                onChanged: _onSearchChanged,
                focusNode: _focusNode,
                decoration: InputDecoration(
                  hintText: "Search users...",
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.search),
                  filled: _isFocused,
                  fillColor: AppColors.of(context).cardBackground,
                  contentPadding: EdgeInsets.symmetric(
                    vertical: screenWidth * 0.01,
                    horizontal: screenWidth * 0.03,
                  ),
                ),
                style: TextStyle(backgroundColor: Colors.transparent),
              ),
            ),
            SizedBox(height: screenWidth * 0.02),
            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : _buildUserList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserList() {
    return ListView.builder(
      itemCount: _users.length,
      itemBuilder: (context, index) {
        Map<String, dynamic> user = _users[index];
        return Padding(
          padding: EdgeInsets.symmetric(
            vertical: MediaQuery.of(context).size.height * 0.0001,
          ),
          child: ListTile(
            leading: CircleAvatar(
              backgroundImage: NetworkImage(user['photoUrl']),
              radius: MediaQuery.of(context).size.width * 0.06,
            ),
            title: Text(user['name']),
            onTap: () => Navigator.pushNamed(context, RoutePaths.profileScreen,
                arguments: {
                  'name': user['name'],
                  'photoUrl': user['photoUrl'],
                  'isCurrentUser': false,
                  'followers': 0,
                  'following': 0,
                  'balance': user['userBalance']
                }),
          ),
        );
      },
    );
  }
}
