import 'package:flutter/material.dart';
import 'dart:math' as math;

class CustomPullToRefresh extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double triggerDistance;
  final Color? backgroundColor;

  const CustomPullToRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.triggerDistance = 100.0,
    this.backgroundColor,
  });

  @override
  State<CustomPullToRefresh> createState() => _CustomPullToRefreshState();
}

class _CustomPullToRefreshState extends State<CustomPullToRefresh>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pullController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pullAnimation;

  double _pullDistance = 0.0;
  bool _isRefreshing = false;
  bool _canRefresh = true;

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pullController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _pullAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pullController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pullController.dispose();
    super.dispose();
  }

  void _handlePullUpdate(double delta) {
    if (!_canRefresh || _isRefreshing) return;

    setState(() {
      _pullDistance = math.max(0, _pullDistance + delta);
      _pullDistance = math.min(_pullDistance, widget.triggerDistance * 1.5);
    });

    // Animate pull controller based on pull distance
    final progress = (_pullDistance / widget.triggerDistance).clamp(0.0, 1.0);
    _pullController.value = progress;
  }

  void _handlePullEnd() {
    if (!_canRefresh || _isRefreshing) return;

    if (_pullDistance >= widget.triggerDistance) {
      _triggerRefresh();
    } else {
      _resetPull();
    }
  }

  void _triggerRefresh() async {
    setState(() {
      _isRefreshing = true;
      _canRefresh = false;
    });

    _rotationController.repeat();
    _pullController.forward();

    try {
      await widget.onRefresh();
    } finally {
      _rotationController.stop();
      _rotationController.reset();
      _resetPull();

      setState(() {
        _isRefreshing = false;
      });

      // Allow refresh again after a short delay
      Future.delayed(const Duration(milliseconds: 500), () {
        setState(() {
          _canRefresh = true;
        });
      });
    }
  }

  void _resetPull() {
    _pullController.reverse().then((_) {
      setState(() {
        _pullDistance = 0.0;
      });
    });
  }

  Widget _buildCricketBall() {
    return AnimatedBuilder(
      animation: Listenable.merge([_rotationAnimation, _pullAnimation]),
      builder: (context, child) {
        final scale = 0.5 + (_pullAnimation.value * 0.5);
        return Transform.scale(
          scale: scale,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: const RadialGradient(
                  colors: [
                    Color(0xFFDC143C), // Crimson red
                    Color(0xFF8B0000), // Dark red
                  ],
                  stops: [0.3, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: CustomPaint(
                painter: CricketBallPainter(),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanUpdate: (details) {
        _handlePullUpdate(details.delta.dy);
      },
      onPanEnd: (details) {
        _handlePullEnd();
      },
      child: Stack(
        children: [
          // Background overlay during pull
          AnimatedBuilder(
            animation: _pullAnimation,
            builder: (context, child) {
              return Container(
                color: (widget.backgroundColor ?? Colors.grey.shade100)
                    .withValues(alpha: _pullAnimation.value * 0.3),
              );
            },
          ),

          // Main content with transform
          AnimatedBuilder(
            animation: _pullAnimation,
            builder: (context, child) {
              final translateY = _pullDistance * 0.5;
              return Transform.translate(
                offset: Offset(0, translateY),
                child: widget.child,
              );
            },
          ),

          // Cricket ball indicator
          AnimatedBuilder(
            animation: _pullAnimation,
            builder: (context, child) {
              final opacity = _pullAnimation.value;
              final translateY = _pullDistance * 0.3;

              return Positioned(
                top: translateY - 25,
                left: MediaQuery.of(context).size.width / 2 - 25,
                child: Opacity(
                  opacity: opacity,
                  child: _buildCricketBall(),
                ),
              );
            },
          ),

          // Pull instruction text
          if (_pullDistance > 20 && !_isRefreshing)
            AnimatedBuilder(
              animation: _pullAnimation,
              builder: (context, child) {
                final opacity = (_pullAnimation.value * 2).clamp(0.0, 1.0);
                final translateY = _pullDistance * 0.3;

                return Positioned(
                  top: translateY + 40,
                  left: 0,
                  right: 0,
                  child: Opacity(
                    opacity: opacity,
                    child: Center(
                      child: Text(
                        _pullDistance >= widget.triggerDistance
                            ? 'Release to refresh'
                            : 'Pull down to refresh',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }
}

class CricketBallPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw the seam (curved line across the ball)
    final path = Path();

    // Left curve
    path.moveTo(center.dx - radius * 0.8, center.dy - radius * 0.3);
    path.quadraticBezierTo(
      center.dx - radius * 0.3, center.dy - radius * 0.1,
      center.dx, center.dy,
    );

    // Right curve
    path.quadraticBezierTo(
      center.dx + radius * 0.3, center.dy + radius * 0.1,
      center.dx + radius * 0.8, center.dy + radius * 0.3,
    );

    canvas.drawPath(path, paint);

    // Draw stitching marks
    final stitchPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // Left side stitches
    for (int i = 0; i < 6; i++) {
      final t = i / 5.0;
      final x = center.dx - radius * 0.6 + (radius * 0.6 * t);
      final y = center.dy - radius * 0.2 + (radius * 0.4 * t);

      canvas.drawLine(
        Offset(x - 3, y - 1),
        Offset(x + 3, y + 1),
        stitchPaint,
      );
    }

    // Right side stitches
    for (int i = 0; i < 6; i++) {
      final t = i / 5.0;
      final x = center.dx + (radius * 0.6 * t);
      final y = center.dy - radius * 0.1 + (radius * 0.2 * t);

      canvas.drawLine(
        Offset(x - 3, y - 1),
        Offset(x + 3, y + 1),
        stitchPaint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
