import 'dart:convert';

class Match {
  final String matchType;
  final String matchId;
  final String matchFormat;
  final String team1;
  final String team2;
  final String team1ImagePath;
  final String team2ImagePath;
  final String team1Id;
  final String team2Id;
  final String startDate;
  String matchState;
  final String contestCount;
  final String result;

  Match({
    required this.matchType,
    required this.matchId,
    required this.matchFormat,
    required this.team1,
    required this.team2,
    required this.team1ImagePath,
    required this.team2ImagePath,
    required this.team1Id,
    required this.team2Id,
    required this.startDate,
    required this.matchState,
    required this.contestCount,
    required this.result,
  });

  factory Match.fromJson(Map<String, dynamic> json) {
    return Match(
      matchType: json['matchType'],
      matchId: json['matchId'],
      matchFormat: json['matchFormat'],
      team1: json["team1"]["teamSName"],
      team2: json["team2"]["teamSName"],
      team1ImagePath: json["team1"]["imagePath"],
      team2ImagePath: json["team2"]["imagePath"],
      team1Id: json["team1"]["teamId"],
      team2Id: json["team2"]["teamId"],
      startDate: json['timeStamp'].toString(),
      matchState: json['matchState'],
      contestCount: json['contestCount'] ?? "",
      result: json['result'] ?? "",
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'matchType': matchType,
      'matchId': matchId,
      'matchFormat': matchFormat,
      'team1': {
        'teamSName': team1,
        'imagePath': team1ImagePath,
        'teamId': team1Id,
      },
      'team2': {
        'teamSName': team2,
        'imagePath': team2ImagePath,
        'teamId': team2Id,
      },
      'timeStamp': startDate,
      'matchState': matchState,
      'contestCount': contestCount,
      'result': result,
    };
  }

  @override
  String toString() {
    return jsonEncode(toJson());
  }

  String get team1ImageId => team1ImagePath;
  String get team2ImageId => team2ImagePath;
  String get state => matchState;
}
