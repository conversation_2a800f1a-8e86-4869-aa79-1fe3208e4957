import 'package:flutter/foundation.dart';

enum LogLevel {
  debug,
  info,
  warning,
  error,
  none,
}

class Logger {
  static LogLevel _currentLevel = kDebugMode ? LogLevel.debug : LogLevel.info;
  static bool _logToConsole = true;

  static void init({
    LogLevel logLevel = LogLevel.info,
    bool logToConsole = true,
  }) {
    _currentLevel = kDebugMode ? LogLevel.debug : logLevel;
    _logToConsole = logToConsole;

    info('Logger initialized with level: $_currentLevel');
  }

  static void close() {
  }

  static void setLevel(LogLevel level) {
    _currentLevel = level;
    info('Log level changed to: $level');
  }

  static void debug(String message, {Object? error, StackTrace? stackTrace, String? tag}) {
    if (_currentLevel.index <= LogLevel.debug.index) {
      _log('DEBUG', message, error: error, stackTrace: stackTrace, tag: tag);
    }
  }

  static void info(String message, {Object? error, StackTrace? stackTrace, String? tag}) {
    if (_currentLevel.index <= LogLevel.info.index) {
      _log('INFO', message, error: error, stackTrace: stackTrace, tag: tag);
    }
  }

  static void warn(String message, {Object? error, StackTrace? stackTrace, String? tag}) {
    if (_currentLevel.index <= LogLevel.warning.index) {
      _log('WARN', message, error: error, stackTrace: stackTrace, tag: tag);
    }
  }

  static void error(String message, {Object? error, StackTrace? stackTrace, String? tag}) {
    if (_currentLevel.index <= LogLevel.error.index) {
      _log('ERROR', message, error: error, stackTrace: stackTrace, tag: tag);
    }
  }

  static void _log(String level, String message,
      {Object? error, StackTrace? stackTrace, String? tag}) {
    if (_currentLevel == LogLevel.none) return;

    final timestamp = DateTime.now().toIso8601String();
    final tagStr = tag != null ? '[$tag] ' : '';
    final logMessage = '[$timestamp] $level: $tagStr$message';

    if (_logToConsole) {
      debugPrint(logMessage);

      if (error != null) {
        debugPrint('[$timestamp] $level: Error details: $error');
      }

      if (stackTrace != null) {
        debugPrint('[$timestamp] $level: Stack trace: $stackTrace');
      }
    }
  }
}
