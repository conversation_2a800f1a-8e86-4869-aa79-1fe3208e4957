import 'dart:math';

import 'package:flutter/material.dart';
import '../../../backend/model/contest.dart';
import '../../../backend/model/match.dart';
import '../../../backend/rest/contest_details.dart';
import '../../widgets/cards/contest_card.dart';
import '../../widgets/utils/contest_app_bar.dart';

class ShowContests extends StatefulWidget {
  final Match? match;
  final ContestAppBar? contestAppBar;

  const ShowContests(this.match, this.contestAppBar, {super.key});

  @override
  _ShowContestsState createState() => _ShowContestsState();
}

class _ShowContestsState extends State<ShowContests> {
  List<Contest> contests = [];

  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    try {
      Map matchContestDetailsMap =
          await ContestDetails.getMatchContestDetails(widget.match!.matchId);

      setState(() {
        for (var entry in matchContestDetailsMap.entries) {
          contests.add(Contest(
              entry.key + "." + Random().nextInt(999999999).toString(),
              entry.key,
              entry.value));
        }
      });
    } catch (e) {
      print("Error fetching data: $e");
      // Handle error, show error message, etc.
    }
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      appBar: widget.contestAppBar,
      body: Padding(
        padding: EdgeInsets.all(screenWidth * 0.04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Text(
              "All Contests",
              style: TextStyle(
                fontSize: screenWidth * 0.05,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: screenHeight * 0.02),
            Expanded(
              child: contests.isEmpty
                  ? const Center(
                      child:
                          CircularProgressIndicator(), // Show circular progress indicator when contests are being fetched
                    )
                  : ListView.builder(
                      itemCount: contests.length,
                      itemBuilder: (BuildContext context, int index) {
                        Contest contest = contests[index];
                        return Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: screenHeight * 0.01,
                            horizontal: screenWidth * 0.01,
                          ),
                          child: ContestCard(
                            contest,
                            widget.match,
                            widget.contestAppBar!,
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
