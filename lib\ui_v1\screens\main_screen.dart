import 'package:flutter/material.dart';
import '../themes/app_colors.dart';
import '../widgets/utils/app_bar_widget.dart';
import 'side_menu.dart';
import 'states/my_matches_screen.dart';
import 'states/home_screen.dart';
import 'states/search_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  _MainScreenState createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  // List of screens to display
  final List<Widget> _screens = [
    const HomeScreen(), // HomeScreen is instantiated here
    const MyMatchesScreen(), // MyMatchesScreen
    // const CustomContestScreen(), // CustomContestScreen
    const SearchScreen(), // SearchScreen
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    return DefaultTabController(
        length: 3,
        child: Scaffold(
          appBar: AppBarWidget(
              title: Image.asset(
                'lib/UI_V1/assets/icons/Top3Icon.png',
                height: 35,
                width: 35,
              ),
              showTabBar: _selectedIndex == 1),
          drawer: Drawer(
            width: screenWidth * 0.9,
            child: const SideMenu(),
          ),
          body: _screens[_selectedIndex],
          bottomNavigationBar: BottomNavigationBar(
            items: const <BottomNavigationBarItem>[
              BottomNavigationBarItem(
                icon: Icon(Icons.home_outlined), // Outline icon for Home
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons
                    .sports_baseball_outlined), // Outline icon for My Matches
                label: 'My Matches',
              ),
              // BottomNavigationBarItem(
              //   icon: Icon(Icons
              //       .add_circle_outline), // Outline icon for Create Contest
              //   label: 'Create Contest',
              // ),
              BottomNavigationBarItem(
                icon: Icon(Icons.search_outlined), // Outline icon for Search
                label: 'Search',
              ),
            ],
            currentIndex: _selectedIndex,
            selectedItemColor: const Color.fromARGB(
                255, 48, 0, 180), // Color for the selected item
            unselectedItemColor: Colors.grey, // Color for unselected items
            backgroundColor: AppColors.of(context)
                .cardBackground, // Background color of the navigation bar
            type: BottomNavigationBarType
                .fixed, // Use fixed type for better appearance
            elevation: 8, // Elevation for shadow effect
            onTap: _onItemTapped, // Change state on tap
          ),
        ));
  }
}
