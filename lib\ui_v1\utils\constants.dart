/// Defines route paths for navigation throughout the application.
///
/// Used with the Navigator to navigate between screens.
class RoutePaths {
  /// Main screen (home)
  static const String main = '/';

  /// Login and registration screen
  static const String loginRegister = '/loginRegister';

  /// Screen showing all available contests
  static const String showContests = '/showContests';

  /// Screen showing details of a specific contest
  static const String showContest = '/showContest';

  /// Route for joining a contest
  static const String joinContest = 'joinContest';

  /// Screen showing contests the user has joined
  static const String joinedContests = '/joinedContests';

  /// Wallet screen for managing funds
  static const String wallet = '/wallet';

  /// User profile screen
  static const String profileScreen = '/profileScreen';

  /// Success screen shown after completing actions
  static const String successScreen = '/successScreen';

  /// Leaderboard/ranking screen
  static const String rankScreen = '/rankScreen';

  /// Payment processing screen
  static const String paymentScreen = '/paymentScreen';

  /// Transaction history screen
  static const String allTransactions = "/transactions";

  /// Notifications screen
  static const String notifications = "/notifications";

  /// Withdrawals screen
  static const String withdrawals = "/withdrawals";

  /// User information and settings screen
  static const String infoAndSettings = "/infoAndSettings";

  /// Google sign-in screen
  static const String googleSignInScreen = "/googleSignInScreen";

  /// Phone sign-in screen
  static const String phoneSignInScreen = "/phoneSignInScreen";
}
