import 'package:flutter/material.dart';

import '../../backend/Model/User.dart';
import '../../backend/model/player.dart';
import '../../backend/rest/match_details.dart';
import '../widgets/utils/top3_widget.dart';

class RankScreen extends StatefulWidget {
  final String upcomingMatchId;
  final String contestEntry;

  const RankScreen({super.key, required this.upcomingMatchId, required this.contestEntry});

  @override
  RankScreenState createState() => RankScreenState();
}

class RankScreenState extends State<RankScreen> {
  late Future<List<Player>> top3ListFuture;
  late Future<List<User>> userRankFuture;

  @override
  void initState() {
    super.initState();
    top3ListFuture = fetchTop3List();
    userRankFuture = getUserContestRank() as Future<List<User>>;
  }

  Future<List<Player>> fetchTop3List() async {
    try {
      List<Map<String, dynamic>> list =
          await MatchDetails.getMatchTop3PlayerDetails(widget.upcomingMatchId);

      return [
        Player.fromMap(list[0]),
        Player.fromMap(list[1]),
        Player.fromMap(list[2])
      ];
    } catch (e) {
      return [];
    }
  }

  Future<List<Object>> getUserContestRank() async {
    try {
      return await MatchDetails.getUserContestRank(
          widget.upcomingMatchId, widget.contestEntry);
    } catch (e) {
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Top 3 Players'),
      ),
      body: FutureBuilder<List<Player>>(
        future: top3ListFuture,
        builder: (BuildContext context, AsyncSnapshot<List<Player>> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No data available.'));
          } else {
            return Column(
              children: [
                const SizedBox(height: 10),
                const Text(
                  "Match Top 3",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color.fromARGB(255, 48, 0, 180),
                  ),
                ),
                const SizedBox(height: 20),
                Top3Widget(
                  top3List: snapshot.data!,
                  saved: true,
                  showPoints: true,
                  onTop3ListChanged: (List<Player> a) {},
                ),
                const SizedBox(height: 14),
                const Padding(
                  padding: EdgeInsets.only(left: 16),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Users Ranking',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                FutureBuilder<List<User>>(
                  future: userRankFuture,
                  builder: (BuildContext context,
                      AsyncSnapshot<List<User>> snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (snapshot.hasError) {
                      return Center(child: Text('Error: ${snapshot.error}'));
                    } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                      return const Center(child: Text('Rank data unavailable.'));
                    } else {
                      return Wrap(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.orange.withOpacity(0.3),
                              border: Border.all(
                                color: Colors.orange,
                                width: 2.0,
                              ),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            margin: const EdgeInsets.all(8.0),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundImage:
                                    NetworkImage(snapshot.data![0].photoUrl),
                              ),
                              title: Text(snapshot.data![0].name),
                              trailing: Text('Rank: ${snapshot.data![0].rank}'),
                            ),
                          ),
                          SizedBox(
                              height: MediaQuery.of(context).size.height * 0.46,
                              child: ListView.builder(
                                shrinkWrap: true,
                                itemCount: snapshot.data!.length - 1,
                                itemBuilder: (context, index) {
                                  User user = snapshot.data![index + 1];
                                  return ListTile(
                                    leading: CircleAvatar(
                                      backgroundImage:
                                          NetworkImage(user.photoUrl),
                                    ),
                                    title: Text(user.name),
                                    trailing: Text('Rank: ${user.rank}'),
                                  );
                                },
                              ))
                        ],
                      );
                    }
                  },
                ),
              ],
            );
          }
        },
      ),
    );
  }
}


