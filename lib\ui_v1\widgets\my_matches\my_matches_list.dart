import 'package:flutter/material.dart';
import '../../../backend/model/match.dart';
import 'base_match_list.dart';

class MyMatchesList extends BaseMatchList {
  MyMatchesList(
      {super.key,
      required super.list,
      required super.emptyTitle,
      required super.onRefresh});

  @override
  _MyMatchesListState createState() => _MyMatchesListState();
}

class _MyMatchesListState extends BaseMatchListState
    with SingleTickerProviderStateMixin {
  @override
  TickerProvider get vsyncProvider => this;

  @override
  Future<List<Match>> getMoreItems() {
    return Future.value(List<Match>.empty());
  }
}
