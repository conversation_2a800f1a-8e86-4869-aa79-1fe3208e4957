import 'package:flutter/material.dart';

import '../../backend/model/player.dart';
import '../utils/constants.dart';
import '../widgets/success_match_widget.dart';
import '../widgets/utils/big_blue_button.dart';
import '../widgets/utils/contest_app_bar.dart';
import '../widgets/utils/top3_widget.dart';
import '../../backend/model/match.dart';

class SuccessScreen extends StatelessWidget {
  final List<Player> top3List;
  final List<IconData> iconList;
  final Match match;
  final ContestAppBar contestAppBar;
  final bool joinedContest;

  const SuccessScreen(this.top3List, this.iconList, this.match,
      this.contestAppBar, this.joinedContest,
      {super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        Card(
          margin: const EdgeInsets.all(16.0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.0),
          ),
          elevation: 5,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Congratulations',
                  style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 0, 138, 5)),
                ),
                Text(
                  joinedContest
                      ? "You've edited the contest"
                      : "You've joined the contest",
                  style: TextStyle(color: Color.fromARGB(255, 87, 87, 87)),
                ),
                const SizedBox(height: 20),
                SuccessMatchWidget(
                  team1: match.team1,
                  team2: match.team2,
                  team1Flag: match.team1ImagePath,
                  team2Flag: match.team2ImagePath,
                  date: match.startDate,
                ),
                const SizedBox(height: 20),
                const Text(
                  "Your Team",
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 48, 0, 180)),
                ),
                const SizedBox(height: 10),
                Top3Widget(
                  top3List: top3List,
                  saved: true,
                  showPoints: false,
                  onTop3ListChanged: (List<Player> a) {},
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
              left: 35,
              top: 10,
              right: 35,
              bottom: 15), // Add padding around the button
          child: BigBlueButton(
            icon: Icons.add,
            text: 'Join Another Contest',
            onPressed: () async {
              Navigator.pushReplacementNamed(
                context,
                RoutePaths.showContests,
                arguments: {match, contestAppBar},
              );
            },
          ),
        ),
        GestureDetector(
          onTap: () {
            Navigator.pushReplacementNamed(context, RoutePaths.main);
          },
          child: const Text(
            'Go Back To Home',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color.fromARGB(
                  255, 48, 0, 180), // Change text color to blue for hyperlinks
              decoration: TextDecoration.underline, // Underline the text
            ),
          ),
        ),
      ]),
    );
  }
}
