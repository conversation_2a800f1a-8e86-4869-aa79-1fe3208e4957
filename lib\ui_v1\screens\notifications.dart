import 'package:flutter/material.dart';

import '../themes/app_colors.dart';
import '../widgets/cards/follow_request_card.dart';
import '../widgets/cards/notification_card.dart';

class Notifications extends StatefulWidget {
  const Notifications({super.key});

  @override
  _NotificationsState createState() => _NotificationsState();
}

class _NotificationsState extends State<Notifications> {
  bool showNotifications = true;
  int followRequestCount = 0;
  int notificationCount = 0;

  Widget _buildTab(String title, bool isActive, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(
            color: isActive
                ? AppColors.of(context).cardBackground
                : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Center(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isActive
                    ? AppColors.of(context).buttonBackground
                    : AppColors.of(context).cardText,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Notifications",
          style: TextStyle(
              color: AppColors.of(context).cardText,
              fontWeight: FontWeight.bold),
        ),
      ),
      body: Column(
        children: [
          SizedBox(
            height: 8,
          ),
          Card(
            elevation: 1,
            color: Colors.transparent,
            child: Container(
              margin: const EdgeInsets.all(4),
              child: Row(
                children: [
                  _buildTab(
                    "Notifications${notificationCount > 0 ? ' (${notificationCount > 99 ? '99+' : notificationCount})' : ''}",
                    showNotifications,
                    () => setState(() => showNotifications = true),
                  ),
                  _buildTab(
                    "Follow Requests${followRequestCount > 0 ? ' (${followRequestCount > 99 ? '99+' : followRequestCount})' : ''}",
                    !showNotifications,
                    () => setState(() => showNotifications = false),
                  ),
                ],
              ),
            ),
          ),
          if (showNotifications)
            NotificationCard(
                name: "name",
                message: "message",
                timeAgo: "timeAgo",
                imageUrl: "imageUrl")
          else
            FollowRequestCard(
                name: "name",
                message: "message",
                timeAgo: "timeAgo",
                imageUrl: "imageUrl",
                onConfirm: () {},
                onDelete: () {})
        ],
      ),
    );
  }
}
