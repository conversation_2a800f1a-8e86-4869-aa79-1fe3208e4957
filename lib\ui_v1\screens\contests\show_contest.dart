import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../../auth/auth_details.dart';
import '../../../backend/model/contest.dart';
import '../../../backend/model/player.dart';
import '../../../backend/model/match.dart';
import '../../../backend/rest/match_details.dart';
import '../../../backend/rest/contest_details.dart';
import '../../../backend/rest/user_details.dart';
import '../../themes/app_colors.dart';
import '../../utils/constants.dart';
import '../../utils/user_context.dart';
import '../../widgets/payment_service.dart';
import '../../widgets/player_selection_widget.dart';
import '../../widgets/utils/add_amount_button.dart';
import '../../widgets/utils/amount_text_box.dart';
import '../../widgets/utils/bottom_modal_line.dart';
import '../../widgets/utils/contest_app_bar.dart';
import '../../widgets/utils/top3_widget.dart';
import '../../widgets/utils/big_blue_button.dart';
import '../../widgets/utils/bottom_modal_top_bar.dart';

class ShowContest extends StatefulWidget {
  final Match? match;
  final ContestAppBar? contestAppBar;
  final Contest? contest;
  final List<Player>? top3List;

  const ShowContest(
      {super.key, this.contest, this.match, this.contestAppBar, this.top3List});

  @override
  _ShowContestState createState() => _ShowContestState();
}

class _ShowContestState extends State<ShowContest> {
  bool saved = false;
  List<Player> teamAPlayers = [];
  List<Player> teamBPlayers = [];

  late List<IconData> iconList;
  late List<Player> top3List;

  bool isLoading = true;
  bool joinedContest = false;
  bool isAnnounced = false;

  final PaymentService _paymentService = PaymentService();

  void updateTop3ListCallback(List<Player> newList) {
    setState(() {
      top3List = newList;
    });
  }

  late Widget? _currentBody;
  bool paymentScreen = false;

  final TextEditingController _textEditingController = TextEditingController();
  @override
  void initState() {
    super.initState();
    top3List = widget.top3List ??
        [
          Player(
              name: "name",
              playerId: "playerId",
              imagePath: "imagePath",
              teamId: "teamId",
              status: false),
          Player(
              name: "name",
              playerId: "playerId",
              imagePath: "imagePath",
              teamId: "teamId",
              status: false),
          Player(
              name: "name",
              playerId: "playerId",
              imagePath: "imagePath",
              teamId: "teamId",
              status: false)
        ];
    if (widget.top3List != null) {
      iconList = [Icons.person_2, Icons.person_2, Icons.person_2];
      joinedContest = true;
    } else {
      iconList = [Icons.person, Icons.person, Icons.person];
      joinedContest = false;
    }
    fetchPlayers();
    _currentBody = null;
  }

  Future<void> fetchPlayers() async {
    try {
      List players = await MatchDetails.getPlayerDetails(widget.match!.matchId);

      Map<String?, List<Player>> teamPlayerMap = HashMap();
      for (var player in players) {
        teamPlayerMap.putIfAbsent(player.teamId, () => []).add(player);
        if (player.status == true) {
          isAnnounced = true;
        }
      }
      setState(() {
        teamAPlayers = teamPlayerMap[widget.match!.team1Id]!.toList();
        teamBPlayers = teamPlayerMap[widget.match!.team2Id]!.toList();

        teamAPlayers.sort(
            (a, b) => (b.status == true ? 1 : 0) - (a.status == true ? 1 : 0));
        teamBPlayers.sort(
            (a, b) => (b.status == true ? 1 : 0) - (a.status == true ? 1 : 0));

        isLoading = false;
      });
      // Assign players of Team B
    } catch (error) {
      print('Error fetching players: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: widget.contestAppBar,
        body: isLoading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      "Select your top 3 players for the match",
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width * 0.05,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Avatar holders
                      Top3Widget(
                        top3List: top3List,
                        saved: saved,
                        showPoints: false,
                        onTop3ListChanged: updateTop3ListCallback,
                      ),
                      PlayerSelectionWidget(
                        teamAPlayers: teamAPlayers,
                        teamBPlayers: teamBPlayers,
                        match: widget.match!,
                        top3List: top3List,
                        iconList: iconList,
                        isAnnounced: isAnnounced,
                        onTop3ListChanged: updateTop3ListCallback,
                      ),

                      // _buildTeamPlayersSection(),
                      _buildSaveTeamButton(),
                    ],
                  ),
                ],
              ));
  }

  Widget _buildSaveTeamButton() {
    bool allNamesAreDefault = top3List.every((player) => player.name != "name");

    return allNamesAreDefault
        ? Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: BigBlueButton(
              icon: null,
              text: 'Save Team',
              onPressed: () {
                saved = true;
                _currentBody ??= _buildJoinContestModal();
                _showModal();
              },
            ),
          )
        : const SizedBox.shrink();
  }

  void _showModal() {
    setState(() {
      isLoading = false;
    });
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        double modalHeight = MediaQuery.of(context).size.height;
        if (paymentScreen) {
          modalHeight *= 0.8;
        } else {
          modalHeight *= 0.5;
        }
        return Container(
          decoration: BoxDecoration(
            color: AppColors.of(context).cardBackground,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(15),
              topRight: Radius.circular(15),
            ),
          ),
          height: modalHeight, // Use 90% of the screen height
          child: _currentBody,
        );
      },
    ).then((value) {
      setState(() {
        saved = false;
      });
    });
  }

  Widget _buildJoinContestModal() {
    return Column(
      children: [
        const BottomModalLine(),
        const Padding(
          padding: EdgeInsets.all(16.0),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'Players Selected',
              style: TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 20),
        Top3Widget(
          top3List: top3List,
          saved: saved,
          showPoints: false,
          onTop3ListChanged: (updateTop3ListCallback),
        ),
        const SizedBox(height: 40),
        _buildJoinContestButton(),
      ],
    );
  }

  Widget _buildJoinContestButton() {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: BigBlueButton(
          icon: null,
          text: joinedContest ? 'Update Contest' : 'Join Contest',
          onPressed: () async {
            Navigator.of(context).pop();
            setState(() {
              isLoading = true;
            });
            var userDetails =
                await UserDetails.getUserDetails(UserContext.userId);
            AuthDetails.setUserBalance(userDetails);
            UserContext.refreshBalance();

            if (double.parse(widget.contest!.entry) <=
                double.parse(UserContext.userBalance)) {
              joinContest();
            } else {
              setState(() {
                paymentScreen = true;
                _currentBody = _buildPaymentModal();
              });
              _showModal();
            }
          },
        ));
  }

  void joinContest() async {
    int responseCode = await saveContest();
    setState(() {
      isLoading = false;
    });
    if (responseCode == 200) {
      await UserDetails.getUserDetails(UserContext.userId).then((userDetails) {
        AuthDetails.setUserBalance(userDetails);
      });
      UserContext.refreshBalance();
      Navigator.pushReplacementNamed(
        context,
        RoutePaths.successScreen,
        arguments: {
          iconList,
          top3List,
          widget.match!,
          widget.contestAppBar,
          joinedContest
        },
      );
    }
  }

  Future<int> saveContest() async {
    final response = joinedContest
        ? await ContestDetails.updateUserContestDetails(
            UserContext.userId,
            widget.match!.matchId,
            widget.contest!.contestId,
            top3List[0].playerId!,
            top3List[1].playerId!,
            top3List[2].playerId!)
        : await ContestDetails.saveUserContestDetails(
            UserContext.userId,
            widget.match!.matchId,
            widget.contest!.contestId,
            top3List[0].playerId!,
            top3List[1].playerId!,
            top3List[2].playerId!);
    return response.statusCode;
  }

  Widget _buildPaymentModal() {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;
    double amount = 0.0;
    return Column(
      children: [
        const BottomModalLine(),
        const Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.warning_rounded,
                        color: Color.fromARGB(255, 200, 0, 0),
                      ),
                      SizedBox(width: 5),
                      Text(
                        'Insufficient Balance',
                        style: TextStyle(
                            color: Color.fromARGB(255, 200, 0, 0),
                            fontSize: 16),
                      ),
                      SizedBox(width: 5),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 15),
              Text(
                'The money in your wallet is not enough to join this contest. Please add amount to your wallet. ',
                style: TextStyle(
                    color: Color.fromARGB(255, 56, 56, 56), fontSize: 15),
              ),
            ],
          ),
        ),
        BottomModalTopBar(
          addBalanceText: 'Add Balance',
          currentBalanceText: 'Current Balance: ₹ ${UserContext.userBalance}',
        ),
        AmountTextBox(
          controller: _textEditingController,
          labelText: 'Enter amount to add',
        ),
        // const SizedBox(height: 30),
        AddAmountButton(
          textEditingController: _textEditingController,
          onPressed: () {
            _startTransaction(amount);
          },
          onValueSelected: (value) => amount = value,
        ),
        // const SizedBox(height: 20),
        GestureDetector(
          onTap: () {
            setState(() {
              saved = true;
              paymentScreen = false;
              Navigator.of(context).pop();
              _currentBody = _buildJoinContestModal();
              _showModal();
            });
          },
          child: const Text(
            'Back to player selection',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color.fromARGB(
                  255, 48, 0, 180), // Change text color to blue for hyperlinks
              decoration: TextDecoration.underline, // Underline the text
            ),
          ),
        ),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }

  void _startTransaction(double amount) {
    setState(() {
      isLoading = true;
    });

    _paymentService.startTransaction(
      amount,
      () {
        Fluttertoast.showToast(msg: "Transaction Successful!");
        joinContest();
      },
      (errorMessage) {
        Fluttertoast.showToast(msg: errorMessage);
        setState(() {
          isLoading = false;
        });
      },
    );
  }
}

