import 'dart:async';
import 'dart:ui';


class MatchCountdownTimer {
  final String targetDateTime;
  late Duration remainingTime;
  Timer? _timer;
  final void Function(Duration) onTick;
  final VoidCallback onEnd;

  MatchCountdownTimer({
    required this.targetDateTime,
    required this.onTick,
    required this.onEnd,
  }) {
    // Initialize remainingTime after the constructor's body starts executing
    remainingTime = _calculateRemainingTime();
    _startCountdown();
  }

  Duration _calculateRemainingTime() {
    DateTime currentTime = DateTime.now();
    return DateTime.fromMillisecondsSinceEpoch(int.parse(targetDateTime))
        .difference(currentTime);
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      remainingTime = _calculateRemainingTime();
      onTick(remainingTime);
      if (remainingTime.isNegative) {
        onEnd();
        timer.cancel();
      }
    });
  }

  void cancel() {
    _timer?.cancel();
  }
}
