import 'package:flutter/material.dart';
import 'theme_service.dart';

/// Manages the application's theme mode and notifies listeners of changes.
///
/// Uses ChangeNotifier to update the UI when the theme changes.
class ThemeProvider with ChangeNotifier {
  /// Current theme mode (light, dark, or system)
  ThemeMode _themeMode = ThemeMode.system;

  /// Creates a new ThemeProvider and loads the saved theme mode
  ThemeProvider() {
    _loadThemeMode();
  }

  /// Gets the current theme mode
  ThemeMode get themeMode => _themeMode;

  /// Loads the saved theme mode from persistent storage
  Future<void> _loadThemeMode() async {
    final isDarkMode = await ThemeService.getThemeMode();
    _themeMode = isDarkMode ? ThemeMode.dark : ThemeMode.light;
    notifyListeners();
  }

  /// Sets a new theme mode and saves it to persistent storage
  ///
  /// @param mode The new theme mode to set
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    notifyListeners();
    await ThemeService.saveThemeMode(mode == ThemeMode.dark);
  }
}
