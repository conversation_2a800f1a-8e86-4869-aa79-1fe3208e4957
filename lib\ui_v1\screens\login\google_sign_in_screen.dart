import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

import '../../../Auth/Auth.dart';
import '../../../Auth/auth_details.dart';
import '../../../backend/rest/user_details.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';
import '../../routes/routes.dart';
import '../../utils/constants.dart';
import '../../utils/context.dart';
import '../../utils/user_context.dart';

class GoogleSignInScreen extends StatefulWidget {
  const GoogleSignInScreen({super.key});

  @override
  State<GoogleSignInScreen> createState() => _GoogleSignInScreenState();
}

class _GoogleSignInScreenState extends State<GoogleSignInScreen> {
  bool _isLoading = false;

  Future _handleLogin() async {
    setState(() {
      _isLoading = true;
    });

    try {
      User user = await Auth.login();
      if (await UserDetails.userExists(user.uid)) {
        AuthDetails.setIfLoggedIn(true);
        AuthDetails.setUserId(user.uid);
        AuthDetails.setUserEmail(user.email ?? '');
        AuthDetails.setUserName(user.displayName ?? '');
        AuthDetails.setUserPhotoUrl(user.photoURL ?? '');
        final userBalance = await UserDetails.getUserDetails(user.uid);
        AuthDetails.setUserBalance(userBalance);

        await UserContext.init();
        await Context.fetchAndCacheMatches();

        if (mounted) {
          Navigator.pushNamed(context, RoutePaths.main);
        }
      } else {
        if (mounted) {
          Navigator.pushNamed(context, RoutePaths.infoAndSettings, arguments: {
            user.displayName,
            user.uid,
            user.email,
            false,
            user.photoURL
          });
        }
      }
    } on AuthException catch (e) {
      if (mounted) {
        _showErrorDialog(
          'Authentication Error',
          'Error: ${e.message}${e.code != null ? ' (Code: ${e.code})' : ''}',
        );
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog(
          'Login Error',
          'An unexpected error occurred during login. Please try again.',
        );
      }
      Logger.error('Unexpected login error', error: e);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          content: Text(
            message,
            style: const TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                "OK",
                style: TextStyle(color: Colors.black),
              ),
            )
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 48, 0, 180),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(28.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Spacer(flex: 1),
              const Text(
                'Welcome!',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 64,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 30),
                child: Text(
                  'Continue with your G-account to get started.',
                  style: TextStyle(
                    color: Color.fromARGB(179, 193, 193, 193),
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.justify,
                ),
              ),
              const Spacer(flex: 1),
              ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.black87,
                    minimumSize: const Size(double.infinity, 30),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(14),
                    ),
                    elevation: 4,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 10),
                  ),
                  onPressed: _handleLogin,
                  child: _isLoading
                      ? const CircularProgressIndicator(
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        )
                      : const Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundImage: NetworkImage(
                                'https://w7.pngwing.com/pngs/882/225/png-transparent-google-logo-google-logo-google-search-icon-google-text-logo-business.png',
                              ),
                            ),
                            SizedBox(width: 20),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Sign up',
                                  style: TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Row(
                                  children: [
                                    Text(
                                      'with ',
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    Text(
                                      'Google',
                                      style: TextStyle(
                                        fontSize: 25,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        )),
              TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, RoutePaths.phoneSignInScreen);
                },
                child: const Text(
                  "Sign in with Phone Number",
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              const Spacer(flex: 2),
            ],
          ),
        ),
      ),
    );
  }
}
