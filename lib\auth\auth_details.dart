import 'package:shared_preferences/shared_preferences.dart';

class AuthDetails {
  static const String loggedInStatusKey = "LOGGED-IN-STATUS-KEY";
  static const String userIdKey = "USER-ID-KEY";
  static const String userEmailKey = "USER-EMAIL-KEY";
  static const String userNameKey = "USER-NAME-KEY";
  static const String userPhotoUrlKey = "USER-PHOTO-URL-KEY";
  static const String userBalanceKey = "USER-BALANCE-KEY";

  static Future<bool?> getIfLoggedIn() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    return sharedPreferences.getBool(loggedInStatusKey);
  }

  static Future<void> setIfLoggedIn(bool status) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    await sharedPreferences.setBool(loggedInStatusKey, status);
  }

  static Future<String?> getUserId() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    return sharedPreferences.getString(userIdKey);
  }

  static Future<void> setUserId(String userId) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    await sharedPreferences.setString(userIdKey, userId);
  }

  static Future<String?> getUserEmail() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    return sharedPreferences.getString(userEmailKey);
  }

  static Future<void> setUserEmail(String email) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    await sharedPreferences.setString(userEmailKey, email);
  }

  static Future<String?> getUserName() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    return sharedPreferences.getString(userNameKey);
  }

  static Future<void> setUserName(String name) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    await sharedPreferences.setString(userNameKey, name);
  }

  static Future<String?> getUserPhotoUrl() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    return sharedPreferences.getString(userPhotoUrlKey);
  }

  static Future<void> setUserPhotoUrl(String photoUrl) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    await sharedPreferences.setString(userPhotoUrlKey, photoUrl);
  }

  static Future<void> setUserBalance(Map<String, dynamic> userDetails) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    try {
      if (userDetails.containsKey('balance')) {
        await sharedPreferences.setString(
            userBalanceKey, (userDetails['balance']/100).toString());
      } else {
        await sharedPreferences.setString(userBalanceKey, '0.00');
      }
    } catch (e) {
      await sharedPreferences.setString(userBalanceKey, '0.00');
    }
  }

  static Future<String?> getUserBalance() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    return sharedPreferences.getString(userBalanceKey);
  }
}
