import 'package:flutter/material.dart';
import '../../backend/model/game_transaction.dart';
import '../utils/user_context.dart';
import '../widgets/game_transaction_list.dart';

class Wallet extends StatefulWidget {
  const Wallet({super.key});

  @override
  _WalletState createState() => _WalletState();
}

class _WalletState extends State<Wallet> {
  late List<GameTransaction> list = [];

  @override
  void initState() {
    super.initState();
    fetchGameTransactions();
  }

  Future<void> fetchGameTransactions() async {
    List<GameTransaction> completedList = [];
    // await UserDetails.getAllGameTransactions(UserContext.userId);
    setState(() {
      list = completedList;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Wallet'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.account_balance_wallet_outlined,
                  size: 100,
                  color: Color.fromARGB(255, 48, 0, 180),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              "Balance:  ₹ ${UserContext.userBalance}",
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: GameTransactionList(
                list: list,
                emptyTitle: 'No Transactions available',
                onRefresh: fetchGameTransactions,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
