import 'package:flutter/material.dart';
import '../../backend/model/base_transaction.dart';
import '../../backend/rest/contest_details.dart';
import '../utils/user_context.dart';
import '../widgets/transaction_list.dart';

class Transactions extends StatefulWidget {
  const Transactions({super.key});

  @override
  _TransactionsState createState() => _TransactionsState();
}

class _TransactionsState extends State<Transactions> {
  List<BaseTransaction> _transactions = [];
  List<BaseTransaction> _filteredTransactions = [];
  bool _isLoading = true;
  final ScrollController _scrollController = ScrollController();

  String selectedTransaction = "All Transactions";

  final List<String> transactionTypes = [
    "All Transactions",
    "Game Transactions",
    "Bank Transactions",
    "Withdrawals",
  ];

  @override
  void initState() {
    super.initState();
    _fetchTransactions();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _fetchTransactions() async {
    try {
      List<BaseTransaction> transactions =
          await ContestDetails.getAllTransactions(UserContext.userId, 1);
      setState(() {
        _transactions = transactions;
        _isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        _isLoading = false;
        _transactions = [];
        _filteredTransactions = [];
      });
    }
  }

  void _onTransactionTypeChanged(String? newValue) {
    if (newValue != null) {
      setState(() {
        selectedTransaction = newValue;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "All Transactions",
          style: TextStyle(color: Colors.white, fontSize: 20),
        ),
        backgroundColor: const Color.fromARGB(255, 48, 0, 180),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Container(
                  margin: EdgeInsets.all(8),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: selectedTransaction,
                      isExpanded: true,
                      icon: const Icon(Icons.arrow_drop_down),
                      menuMaxHeight: 300,
                      items: transactionTypes.map((String type) {
                        return DropdownMenuItem<String>(
                          value: type,
                          child: Text(type),
                        );
                      }).toList(),
                      onChanged: _onTransactionTypeChanged,
                    ),
                  ),
                ),
                Expanded(
                  child: TransactionList(
                      list: _transactions,
                      emptyTitle: 'No Transactions yet',
                      onRefresh: _fetchTransactions,
                      scrollController: _scrollController,
                      selectedTransaction: selectedTransaction),
                ),
              ],
            ),
    );
  }
}
